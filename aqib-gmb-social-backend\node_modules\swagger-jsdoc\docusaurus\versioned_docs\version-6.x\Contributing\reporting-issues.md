---
sidebar_position: 7
---

# Reporting issues

Before starting a new issue, please [check whether there is an existing one](https://github.com/Surnet/swagger-jsdoc/issues). It is quite possible that the topic you would like to bring up has been discussed already in the past.

In case of an issue which hasn't been considered yet, please include as much information as possible. This will help maintainers and other users relate to your problem and possibly solve it.

Guidelines:

- Describe what you were doing when the issue appeared.
- Add a set of steps to reproduce your issue.
- Include screenshots.
- Give examples on expected vs actual behavior.
- Share your failed attempts: what you have tried and what you have considered.

# Contributing

The project exists thanks to the [many contributors](https://github.com/Surnet/swagger-jsdoc/graphs/contributors) who shared their use cases, questions, comments and suggestions for improvements.

Here's how to jump in and contribute yourself:

- Fork the project and clone it locally.
- Create a branch for each separate topic. [Semantic commit messages](https://gist.github.com/joshbuchea/6f47e86d2510bce28f8e7f42ae84c716) will be highly appreciated.
- Comment your code as if you are going to maintain it in the future.
- Use the rich set of unit tests as an example and add more for the new use cases. This will not only enable you to programatically reproduce your fix faster than setting up an application, but it will also make you super cool! :)
- Push to your changes to the origin of your repository and create a new pull request towards the upstream master. (this repository)
