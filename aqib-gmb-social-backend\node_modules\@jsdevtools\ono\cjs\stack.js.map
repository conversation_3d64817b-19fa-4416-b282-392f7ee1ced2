{"version": 3, "file": "stack.js", "sourceRoot": "", "sources": ["../src/stack.ts"], "names": [], "mappings": ";;;AAEA,MAAM,OAAO,GAAG,OAAO,CAAC;AACxB,MAAM,OAAO,GAAG,WAAW,CAAC;AAc5B;;GAEG;AACH,SAAgB,WAAW,CAAC,SAAyC;IACnE,OAAO,OAAO,CACZ,SAAS;QACT,SAAS,CAAC,YAAY;QACtB,OAAO,SAAS,CAAC,GAAG,KAAK,UAAU,CACpC,CAAC;AACJ,CAAC;AAND,kCAMC;AAED;;GAEG;AACH,SAAgB,eAAe,CAAC,SAAyC;IACvE,OAAO,OAAO;IACZ,uFAAuF;IACvF,CAAC,SAAS;QACV,SAAS,CAAC,QAAQ;QAClB,OAAO,SAAS,CAAC,GAAG,KAAK,UAAU,CACpC,CAAC;AACJ,CAAC;AAPD,0CAOC;AAED;;GAEG;AACH,SAAgB,UAAU,CAAC,QAAmB,EAAE,aAAyB;IACvE,IAAI,QAAQ,GAAG,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;IACxC,IAAI,aAAa,GAAG,aAAa,CAAC,CAAC,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC;IAEpE,IAAI,QAAQ,IAAI,aAAa,EAAE;QAC7B,OAAO,QAAQ,GAAG,MAAM,GAAG,aAAa,CAAC;KAC1C;SACI;QACH,OAAO,QAAQ,IAAI,aAAa,CAAC;KAClC;AACH,CAAC;AAVD,gCAUC;AAED;;GAEG;AACH,SAAgB,cAAc,CAAC,SAAoB,EAAE,QAAmB,EAAE,aAAyB;IACjG,IAAI,aAAa,EAAE;QACjB,MAAM,CAAC,cAAc,CAAC,QAAQ,EAAE,OAAO,EAAE;YACvC,GAAG,EAAE,GAAG,EAAE;gBACR,IAAI,QAAQ,GAAG,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;gBAC7C,OAAO,UAAU,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,EAAE,aAAa,CAAC,CAAC;YACxD,CAAC;YACD,UAAU,EAAE,KAAK;YACjB,YAAY,EAAE,IAAI;SACnB,CAAC,CAAC;KACJ;SACI;QACH,YAAY,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;KACnC;AACH,CAAC;AAdD,wCAcC;AAED;;GAEG;AACH,SAAS,QAAQ,CAAC,KAAyB;IACzC,IAAI,KAAK,EAAE;QACT,IAAI,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QAEjC,qDAAqD;QACrD,IAAI,QAAQ,CAAC;QACb,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACrC,IAAI,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YAEpB,IAAI,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;gBACtB,IAAI,QAAQ,KAAK,SAAS,EAAE;oBAC1B,kDAAkD;oBAClD,mDAAmD;oBACnD,QAAQ,GAAG,CAAC,CAAC;iBACd;aACF;iBACI,IAAI,QAAQ,KAAK,SAAS,EAAE;gBAC/B,+DAA+D;gBAC/D,oDAAoD;gBACpD,KAAK,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,QAAQ,CAAC,CAAC;gBACrC,MAAM;aACP;SACF;QAED,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;YACpB,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SACzB;KACF;IAED,kEAAkE;IAClE,yEAAyE;IACzE,kCAAkC;IAClC,OAAO,KAAK,CAAC;AACf,CAAC;AAED;;GAEG;AACH,SAAS,YAAY,CAAC,KAAgB,EAAE,SAAoB;IAC1D,MAAM,CAAC,cAAc,CAAC,KAAK,EAAE,OAAO,EAAE;QACpC,GAAG,EAAE,GAAG,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QAC/C,UAAU,EAAE,KAAK;QACjB,YAAY,EAAE,IAAI;KACnB,CAAC,CAAC;AACL,CAAC"}