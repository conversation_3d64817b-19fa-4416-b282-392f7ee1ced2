[{"C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\index.tsx": "1", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\reportWebVitals.ts": "2", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\App.tsx": "3", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\theme.tsx": "4", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\store\\index.tsx": "5", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\context\\toast.context.tsx": "6", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\context\\loading.context.tsx": "7", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\actions\\auth.actions.tsx": "8", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\constants\\toastSeverity.constant.tsx": "9", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\forgotPassword\\forgotPassword.screen.tsx": "10", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\signIn\\signIn.screen.tsx": "11", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\dashboard\\dashboard.screen.tsx": "12", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\qanda\\qanda.screen.tsx": "13", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\help\\help.screen.tsx": "14", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\analytics\\analytics.screen.tsx": "15", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\createSocialPost\\createSocialPost.screen.tsx": "16", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\dashboardV2\\dashboardV2.screen.tsx": "17", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessCategory\\businessCategory.screen.tsx": "18", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessCategory\\servicesDemo.screen.tsx": "19", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessCategory\\demo.screen.tsx": "20", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\geoGrid\\geoGrid.screen.tsx": "21", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\userManagement\\roles\\roles.screen.tsx": "22", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\userManagement\\users\\users.screen.tsx": "23", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessManagement\\manageBusiness\\manageBusiness.screen.tsx": "24", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessManagement\\businessSummary\\businessSummary.screen.tsx": "25", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessManagement\\localBusiness\\localBusiness.screen.tsx": "26", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessManagement\\callback\\callback.screen.tsx": "27", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\reviewManagement\\manageReviews\\manageReviews.screen.tsx": "28", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\reducers\\userPreferences.reducer.tsx": "29", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\reducers\\auth.reducer.tsx": "30", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\posts\\listing\\PostListing.screen.tsx": "31", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\loader\\loader.component.tsx": "32", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\unAuthorized\\notFoundPage.component.tsx": "33", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\notFoundPage\\notFoundPage.component.tsx": "34", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\constants\\endPoints.constant.tsx": "35", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\constants\\reducer.constant.tsx": "36", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\httpHelper.service.tsx": "37", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\helperService.tsx": "38", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\constants\\dbConstant.constant.tsx": "39", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\constants\\message.constant.tsx": "40", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\leftMenu\\leftMenu.component.tsx": "41", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\ApplicationHelperService.tsx": "42", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\dashboardV2\\websiteClicksChart.tsx": "43", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\dashboardV2\\platformBreakdownChart.tsx": "44", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\charts\\registeredEmployees.charts.tsx": "45", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\charts\\activeJobs.charts.tsx": "46", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\charts\\pie.charts.tsx": "47", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\homeChartCard\\homeChartCard.component.tsx": "48", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\qanda\\qanda.service.tsx": "49", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\business\\business.service.tsx": "50", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\location\\location.service.tsx": "51", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\replyQuestionAnswer\\replyQuestionAnswer.component.tsx": "52", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\genericDrawer\\genericDrawer.component.tsx": "53", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\revenueChartDashboard\\revenueChartDashboard.component.tsx": "54", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\constants\\application.constant.tsx": "55", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\locationChips\\locationChips.component.tsx": "56", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\dateFilter\\dateFilter.component.tsx": "57", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\locationMetrics\\locationMetrics.service.tsx": "58", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\createSocialPost\\components\\InfoCard.screen.tsx": "59", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\createSocialPost\\components\\submitPost.component.tsx": "60", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\dashboardV2\\searchBreakdown.tsx": "61", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\dashboardV2\\businessProfileInteractionsChart.tsx": "62", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\posts\\posts.service.tsx": "63", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessCategory\\components\\addBusinessCategory.component.tsx": "64", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessCategory\\components\\servicesDisplay.component.tsx": "65", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessCategory\\components\\addServices.component.tsx": "66", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessCategory\\components\\BusinessHours.tsx": "67", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessCategory\\components\\AddSpecialHours.tsx": "68", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\editBusinessName\\editBusinessName.component.tsx": "69", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\moreActivity\\FromTheBusiness.tsx": "70", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\moreActivity\\AddChildren.tsx": "71", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\moreActivity\\MoreAccessibility.tsx": "72", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\moreActivity\\Amenities.tsx": "73", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\moreActivity\\ServiceOptions.tsx": "74", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\moreActivity\\CrowdComponent.tsx": "75", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\moreActivity\\ParkingComponent.tsx": "76", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\moreActivity\\PaymentsComponent.tsx": "77", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\moreActivity\\PlanningComponent.tsx": "78", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\geoGrid\\GeoGridControls.component.tsx": "79", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\geoGrid\\GeoGridMap.component.tsx": "80", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\geoGrid\\geoGrid.service.tsx": "81", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\geoGrid\\GeoGridSettings.component.tsx": "82", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\roles\\roles.service.tsx": "83", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\user\\user.service.tsx": "84", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\confirmModel\\confirmModel.component.tsx": "85", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\addEditUser\\addEditUser.component.tsx": "86", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\auth\\auth.service.tsx": "87", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\alertDialog\\alertDialog.component.tsx": "88", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\noRowsFound\\noRowsFound.component.tsx": "89", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\logosPhotosDisplay\\logsPhotosDisplay.component.tsx": "90", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\categoryDisplay\\categoryDisplay.component.tsx": "91", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\LinearProgressWithLabel\\LinearProgressWithLabel.component.tsx": "92", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\addEditBusiness\\addEditBusiness.component.tsx": "93", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\serviceItemsDisplay\\serviceItemsDisplay.component.tsx": "94", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\regularHoursTable\\regularHoursTable.component.tsx": "95", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\serviceAreaList\\serviceAreaList.component.tsx": "96", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\iconOnAvailability\\iconOnAvailability.component.tsx": "97", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\userAvatarWIthName\\userAvatarWIthName.component.tsx": "98", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\replyReviews\\replyReviews.component.tsx": "99", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\createPost\\createPost.component.tsx": "100", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\createTags\\createTags.component.tsx": "101", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\ratingsStar\\ratingsStar.component.tsx": "102", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\review\\review.service.tsx": "103", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\posts\\listing\\postCard\\postCard.screen.tsx": "104", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\posts\\updatesSection\\updatesSection.screen.tsx": "105", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\actions\\userPreferences.actions.tsx": "106", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\menuListItemNested\\menuListItemNested.component.tsx": "107", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\exportButton\\exportButton.component.tsx": "108", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\header\\header.component.tsx": "109", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\mediaGallery\\mediaGallery.component.tsx": "110", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\userAvatar\\userAvatar.component.tsx": "111", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\editElements\\editElements.component.tsx": "112", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\colorPalette\\colorPalette.component.tsx": "113", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\createPost\\createPostTemplates\\cards\\testimonialCard1\\testimonialCard1.component.tsx": "114", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\createPost\\createPostTemplates\\cards\\testimonialCard3\\testimonialCard3.component.tsx": "115", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\createPost\\createPostTemplates\\cards\\testimonialCard2\\testimonialCard2.component.tsx": "116", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\createPost\\createPostTemplates\\cards\\testimonialCard4\\testimonialCard4.component.tsx": "117", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\createPost\\createPostTemplates\\cards\\testimonialCard5\\testimonialCard5.component.tsx": "118", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\createPost\\createPostTemplates\\cards\\testimonialCard6\\testimonialCard6.component.tsx": "119", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\excelExport.service.ts": "120", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\context\\preferences.context.tsx": "121", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\utils\\googleMaps.utils.ts": "122", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\reviewManagement\\reviewSettings\\reviewSettings.screen.tsx": "123", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\reviewSettings\\reviewSettings.service.tsx": "124", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\reviewManagement\\reviewSettings\\components\\autoReplySettings.component.tsx": "125", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\reviewManagement\\reviewSettings\\components\\createEditTemplate.component.tsx": "126", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\manageAssets\\manageAssets.screen.tsx": "127", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\manageAssets\\manageAssets.service.ts": "128", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\fileGallery\\fileGallery.component.tsx": "129", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\fileUpload\\fileUpload.component.tsx": "130", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\fileViewer\\fileViewer.component.tsx": "131", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\confirmDelete\\confirmDelete.component.tsx": "132", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\utils\\fileUtils.ts": "133", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\manageAssets\\components\\confirmDelete.component.tsx": "134", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\manageAssets\\components\\fileUpload.component.tsx": "135", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\manageAssets\\components\\fileViewer.component.tsx": "136", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\manageAssets\\components\\fileGallery.component.tsx": "137", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessManagement\\instagramCallback\\instagramCallback.screen.tsx": "138", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\InstagramSyncButton\\InstagramSyncButton.tsx": "139", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\instagram\\instagram.service.tsx": "140", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\constants\\dbConstant.constant.ts": "141", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\createSocialPost\\components\\gallerySelection.component.tsx": "142"}, {"size": 1322, "mtime": 1748969368507, "results": "143", "hashOfConfig": "144"}, {"size": 425, "mtime": 1748969368711, "results": "145", "hashOfConfig": "144"}, {"size": 12767, "mtime": 1749136160184, "results": "146", "hashOfConfig": "144"}, {"size": 1924, "mtime": 1748969177420, "results": "147", "hashOfConfig": "144"}, {"size": 1216, "mtime": 1748969371143, "results": "148", "hashOfConfig": "144"}, {"size": 438, "mtime": 1748969368495, "results": "149", "hashOfConfig": "144"}, {"size": 152, "mtime": 1748969368485, "results": "150", "hashOfConfig": "144"}, {"size": 2954, "mtime": 1748969367929, "results": "151", "hashOfConfig": "144"}, {"size": 112, "mtime": 1748969368479, "results": "152", "hashOfConfig": "144"}, {"size": 2586, "mtime": 1748969370932, "results": "153", "hashOfConfig": "144"}, {"size": 10740, "mtime": 1748969371025, "results": "154", "hashOfConfig": "144"}, {"size": 13928, "mtime": 1748969370904, "results": "155", "hashOfConfig": "144"}, {"size": 21620, "mtime": 1748969371012, "results": "156", "hashOfConfig": "144"}, {"size": 220, "mtime": 1748969370943, "results": "157", "hashOfConfig": "144"}, {"size": 72602, "mtime": 1748969179368, "results": "158", "hashOfConfig": "144"}, {"size": 84042, "mtime": 1749136433461, "results": "159", "hashOfConfig": "144"}, {"size": 23584, "mtime": 1748969370918, "results": "160", "hashOfConfig": "144"}, {"size": 4224, "mtime": 1748969368724, "results": "161", "hashOfConfig": "144"}, {"size": 2557, "mtime": 1748969370870, "results": "162", "hashOfConfig": "144"}, {"size": 92339, "mtime": 1749136160527, "results": "163", "hashOfConfig": "144"}, {"size": 12587, "mtime": 1748969370938, "results": "164", "hashOfConfig": "144"}, {"size": 37116, "mtime": 1748969179991, "results": "165", "hashOfConfig": "144"}, {"size": 22457, "mtime": 1748969180011, "results": "166", "hashOfConfig": "144"}, {"size": 25824, "mtime": 1749136160542, "results": "167", "hashOfConfig": "144"}, {"size": 56467, "mtime": 1748969179480, "results": "168", "hashOfConfig": "144"}, {"size": 30332, "mtime": 1748969179542, "results": "169", "hashOfConfig": "144"}, {"size": 4452, "mtime": 1748969370879, "results": "170", "hashOfConfig": "144"}, {"size": 43075, "mtime": 1748969179942, "results": "171", "hashOfConfig": "144"}, {"size": 951, "mtime": 1748969368707, "results": "172", "hashOfConfig": "144"}, {"size": 1730, "mtime": 1748969368697, "results": "173", "hashOfConfig": "144"}, {"size": 18225, "mtime": 1748969179861, "results": "174", "hashOfConfig": "144"}, {"size": 347, "mtime": 1748969178541, "results": "175", "hashOfConfig": "144"}, {"size": 1818, "mtime": 1748969368442, "results": "176", "hashOfConfig": "144"}, {"size": 1814, "mtime": 1748969368320, "results": "177", "hashOfConfig": "144"}, {"size": 5728, "mtime": 1749136433356, "results": "178", "hashOfConfig": "144"}, {"size": 370, "mtime": 1748969368473, "results": "179", "hashOfConfig": "144"}, {"size": 8994, "mtime": 1748969371088, "results": "180", "hashOfConfig": "144"}, {"size": 1053, "mtime": 1748969371080, "results": "181", "hashOfConfig": "144"}, {"size": 307, "mtime": 1748969368464, "results": "182", "hashOfConfig": "144"}, {"size": 1953, "mtime": 1748969178987, "results": "183", "hashOfConfig": "144"}, {"size": 21434, "mtime": 1749136433320, "results": "184", "hashOfConfig": "144"}, {"size": 3091, "mtime": 1748969371041, "results": "185", "hashOfConfig": "144"}, {"size": 3899, "mtime": 1748969179700, "results": "186", "hashOfConfig": "144"}, {"size": 4028, "mtime": 1748969179684, "results": "187", "hashOfConfig": "144"}, {"size": 1918, "mtime": 1748969367988, "results": "188", "hashOfConfig": "144"}, {"size": 1129, "mtime": 1748969367971, "results": "189", "hashOfConfig": "144"}, {"size": 977, "mtime": 1748969367982, "results": "190", "hashOfConfig": "144"}, {"size": 2779, "mtime": 1748969178435, "results": "191", "hashOfConfig": "144"}, {"size": 1388, "mtime": 1748969371120, "results": "192", "hashOfConfig": "144"}, {"size": 2038, "mtime": 1748969371058, "results": "193", "hashOfConfig": "144"}, {"size": 1631, "mtime": 1748969371101, "results": "194", "hashOfConfig": "144"}, {"size": 4825, "mtime": 1748969368417, "results": "195", "hashOfConfig": "144"}, {"size": 971, "mtime": 1748969368089, "results": "196", "hashOfConfig": "144"}, {"size": 4015, "mtime": 1748969178808, "results": "197", "hashOfConfig": "144"}, {"size": 1900, "mtime": 1749136160218, "results": "198", "hashOfConfig": "144"}, {"size": 3524, "mtime": 1748969368215, "results": "199", "hashOfConfig": "144"}, {"size": 6531, "mtime": 1748969178236, "results": "200", "hashOfConfig": "144"}, {"size": 1027, "mtime": 1748969371107, "results": "201", "hashOfConfig": "144"}, {"size": 3176, "mtime": 1748969179606, "results": "202", "hashOfConfig": "144"}, {"size": 42714, "mtime": 1749136433410, "results": "203", "hashOfConfig": "144"}, {"size": 7778, "mtime": 1748969370923, "results": "204", "hashOfConfig": "144"}, {"size": 4054, "mtime": 1748969179667, "results": "205", "hashOfConfig": "144"}, {"size": 2915, "mtime": 1749136433744, "results": "206", "hashOfConfig": "144"}, {"size": 1521, "mtime": 1748969370847, "results": "207", "hashOfConfig": "144"}, {"size": 7403, "mtime": 1748969370859, "results": "208", "hashOfConfig": "144"}, {"size": 8129, "mtime": 1748969370854, "results": "209", "hashOfConfig": "144"}, {"size": 12230, "mtime": 1748969368737, "results": "210", "hashOfConfig": "144"}, {"size": 9701, "mtime": 1748969368730, "results": "211", "hashOfConfig": "144"}, {"size": 7411, "mtime": 1748969368059, "results": "212", "hashOfConfig": "144"}, {"size": 7645, "mtime": 1748969370963, "results": "213", "hashOfConfig": "144"}, {"size": 7764, "mtime": 1748969370948, "results": "214", "hashOfConfig": "144"}, {"size": 8438, "mtime": 1748969370968, "results": "215", "hashOfConfig": "144"}, {"size": 9418, "mtime": 1748969370953, "results": "216", "hashOfConfig": "144"}, {"size": 8390, "mtime": 1748969370988, "results": "217", "hashOfConfig": "144"}, {"size": 7694, "mtime": 1748969370958, "results": "218", "hashOfConfig": "144"}, {"size": 8842, "mtime": 1748969370973, "results": "219", "hashOfConfig": "144"}, {"size": 8910, "mtime": 1748969370978, "results": "220", "hashOfConfig": "144"}, {"size": 9753, "mtime": 1748969370983, "results": "221", "hashOfConfig": "144"}, {"size": 16883, "mtime": 1748969368096, "results": "222", "hashOfConfig": "144"}, {"size": 8642, "mtime": 1748969368161, "results": "223", "hashOfConfig": "144"}, {"size": 3621, "mtime": 1748969371074, "results": "224", "hashOfConfig": "144"}, {"size": 6848, "mtime": 1748969368169, "results": "225", "hashOfConfig": "144"}, {"size": 723, "mtime": 1748969371134, "results": "226", "hashOfConfig": "144"}, {"size": 2655, "mtime": 1748969180196, "results": "227", "hashOfConfig": "144"}, {"size": 2179, "mtime": 1748969177971, "results": "228", "hashOfConfig": "144"}, {"size": 62073, "mtime": 1748969177860, "results": "229", "hashOfConfig": "144"}, {"size": 847, "mtime": 1748969371051, "results": "230", "hashOfConfig": "144"}, {"size": 3505, "mtime": 1748969177878, "results": "231", "hashOfConfig": "144"}, {"size": 825, "mtime": 1748969178665, "results": "232", "hashOfConfig": "144"}, {"size": 4702, "mtime": 1748969368221, "results": "233", "hashOfConfig": "144"}, {"size": 3568, "mtime": 1748969367959, "results": "234", "hashOfConfig": "144"}, {"size": 1109, "mtime": 1748969367946, "results": "235", "hashOfConfig": "144"}, {"size": 9176, "mtime": 1748969177843, "results": "236", "hashOfConfig": "144"}, {"size": 2262, "mtime": 1748969368435, "results": "237", "hashOfConfig": "144"}, {"size": 3305, "mtime": 1748969178736, "results": "238", "hashOfConfig": "144"}, {"size": 1607, "mtime": 1748969368429, "results": "239", "hashOfConfig": "144"}, {"size": 583, "mtime": 1748969368196, "results": "240", "hashOfConfig": "144"}, {"size": 463, "mtime": 1748969368454, "results": "241", "hashOfConfig": "144"}, {"size": 6596, "mtime": 1748969178782, "results": "242", "hashOfConfig": "144"}, {"size": 14893, "mtime": 1748969368007, "results": "243", "hashOfConfig": "144"}, {"size": 8516, "mtime": 1748969178205, "results": "244", "hashOfConfig": "144"}, {"size": 1147, "mtime": 1748969368411, "results": "245", "hashOfConfig": "144"}, {"size": 2167, "mtime": 1748969371126, "results": "246", "hashOfConfig": "144"}, {"size": 12145, "mtime": 1749136433575, "results": "247", "hashOfConfig": "144"}, {"size": 3329, "mtime": 1748969371000, "results": "248", "hashOfConfig": "144"}, {"size": 485, "mtime": 1748969367939, "results": "249", "hashOfConfig": "144"}, {"size": 5996, "mtime": 1748969368238, "results": "250", "hashOfConfig": "144"}, {"size": 2617, "mtime": 1748969368072, "results": "251", "hashOfConfig": "144"}, {"size": 3305, "mtime": 1748969368191, "results": "252", "hashOfConfig": "144"}, {"size": 2809, "mtime": 1748969368226, "results": "253", "hashOfConfig": "144"}, {"size": 1348, "mtime": 1748969368448, "results": "254", "hashOfConfig": "144"}, {"size": 5636, "mtime": 1748969368063, "results": "255", "hashOfConfig": "144"}, {"size": 9514, "mtime": 1748969367995, "results": "256", "hashOfConfig": "144"}, {"size": 2827, "mtime": 1748969368012, "results": "257", "hashOfConfig": "144"}, {"size": 4537, "mtime": 1748969368026, "results": "258", "hashOfConfig": "144"}, {"size": 2097, "mtime": 1748969368019, "results": "259", "hashOfConfig": "144"}, {"size": 3140, "mtime": 1748969368038, "results": "260", "hashOfConfig": "144"}, {"size": 3321, "mtime": 1748969368045, "results": "261", "hashOfConfig": "144"}, {"size": 3000, "mtime": 1748969368053, "results": "262", "hashOfConfig": "144"}, {"size": 13705, "mtime": 1748969371067, "results": "263", "hashOfConfig": "144"}, {"size": 163, "mtime": 1748969368490, "results": "264", "hashOfConfig": "144"}, {"size": 4815, "mtime": 1748969371157, "results": "265", "hashOfConfig": "144"}, {"size": 16593, "mtime": 1748842579937, "results": "266", "hashOfConfig": "144"}, {"size": 4220, "mtime": 1748804384121, "results": "267", "hashOfConfig": "144"}, {"size": 15590, "mtime": 1748806340914, "results": "268", "hashOfConfig": "144"}, {"size": 11587, "mtime": 1749136160751, "results": "269", "hashOfConfig": "144"}, {"size": 15944, "mtime": 1748941966068, "results": "270", "hashOfConfig": "144"}, {"size": 3056, "mtime": 1748931206364, "results": "271", "hashOfConfig": "144"}, {"size": 8554, "mtime": 1748931169711, "results": "272", "hashOfConfig": "144"}, {"size": 10503, "mtime": 1748931116654, "results": "273", "hashOfConfig": "144"}, {"size": 8369, "mtime": 1748931329681, "results": "274", "hashOfConfig": "144"}, {"size": 2576, "mtime": 1748887662915, "results": "275", "hashOfConfig": "144"}, {"size": 2526, "mtime": 1748931004554, "results": "276", "hashOfConfig": "144"}, {"size": 1469, "mtime": 1748934891273, "results": "277", "hashOfConfig": "144"}, {"size": 7042, "mtime": 1748934815415, "results": "278", "hashOfConfig": "144"}, {"size": 7395, "mtime": 1748937395633, "results": "279", "hashOfConfig": "144"}, {"size": 8292, "mtime": 1748937372532, "results": "280", "hashOfConfig": "144"}, {"size": 6889, "mtime": 1749136160534, "results": "281", "hashOfConfig": "144"}, {"size": 2398, "mtime": 1749136160189, "results": "282", "hashOfConfig": "144"}, {"size": 1249, "mtime": 1749136160791, "results": "283", "hashOfConfig": "144"}, {"size": 356, "mtime": 1749136160222, "results": "284", "hashOfConfig": "144"}, {"size": 14998, "mtime": 1749136433364, "results": "285", "hashOfConfig": "144"}, {"filePath": "286", "messages": "287", "suppressedMessages": "288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "qj6s6v", {"filePath": "289", "messages": "290", "suppressedMessages": "291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "292", "messages": "293", "suppressedMessages": "294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 19, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "295", "messages": "296", "suppressedMessages": "297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "298", "messages": "299", "suppressedMessages": "300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "301", "messages": "302", "suppressedMessages": "303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "304", "messages": "305", "suppressedMessages": "306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "307", "messages": "308", "suppressedMessages": "309", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "310", "messages": "311", "suppressedMessages": "312", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "313", "messages": "314", "suppressedMessages": "315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "316", "messages": "317", "suppressedMessages": "318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "319", "messages": "320", "suppressedMessages": "321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "322", "messages": "323", "suppressedMessages": "324", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "325", "messages": "326", "suppressedMessages": "327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "328", "messages": "329", "suppressedMessages": "330", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "331", "messages": "332", "suppressedMessages": "333", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 21, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "334", "messages": "335", "suppressedMessages": "336", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 22, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "337", "messages": "338", "suppressedMessages": "339", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "340", "messages": "341", "suppressedMessages": "342", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "343", "messages": "344", "suppressedMessages": "345", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "346", "messages": "347", "suppressedMessages": "348", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "349", "messages": "350", "suppressedMessages": "351", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "352", "messages": "353", "suppressedMessages": "354", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "355", "messages": "356", "suppressedMessages": "357", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "358", "messages": "359", "suppressedMessages": "360", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 17, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "361", "messages": "362", "suppressedMessages": "363", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "364", "messages": "365", "suppressedMessages": "366", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "367", "messages": "368", "suppressedMessages": "369", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 29, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "370", "messages": "371", "suppressedMessages": "372", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "373", "messages": "374", "suppressedMessages": "375", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "376", "messages": "377", "suppressedMessages": "378", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 15, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "379", "messages": "380", "suppressedMessages": "381", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "382", "messages": "383", "suppressedMessages": "384", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "385", "messages": "386", "suppressedMessages": "387", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "388", "messages": "389", "suppressedMessages": "390", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "391", "messages": "392", "suppressedMessages": "393", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "394", "messages": "395", "suppressedMessages": "396", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "397", "messages": "398", "suppressedMessages": "399", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "400", "messages": "401", "suppressedMessages": "402", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "403", "messages": "404", "suppressedMessages": "405", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "406", "messages": "407", "suppressedMessages": "408", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 18, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "409", "messages": "410", "suppressedMessages": "411", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "412", "messages": "413", "suppressedMessages": "414", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "415", "messages": "416", "suppressedMessages": "417", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "418", "messages": "419", "suppressedMessages": "420", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "421", "messages": "422", "suppressedMessages": "423", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "424", "messages": "425", "suppressedMessages": "426", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "427", "messages": "428", "suppressedMessages": "429", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "430", "messages": "431", "suppressedMessages": "432", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "433", "messages": "434", "suppressedMessages": "435", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "436", "messages": "437", "suppressedMessages": "438", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "439", "messages": "440", "suppressedMessages": "441", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "442", "messages": "443", "suppressedMessages": "444", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "445", "messages": "446", "suppressedMessages": "447", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "448", "messages": "449", "suppressedMessages": "450", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "451", "messages": "452", "suppressedMessages": "453", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "454", "messages": "455", "suppressedMessages": "456", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "457", "messages": "458", "suppressedMessages": "459", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "460", "messages": "461", "suppressedMessages": "462", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "463", "messages": "464", "suppressedMessages": "465", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 33, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "466", "messages": "467", "suppressedMessages": "468", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "469", "messages": "470", "suppressedMessages": "471", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "472", "messages": "473", "suppressedMessages": "474", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "475", "messages": "476", "suppressedMessages": "477", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 20, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "478", "messages": "479", "suppressedMessages": "480", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "481", "messages": "482", "suppressedMessages": "483", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "484", "messages": "485", "suppressedMessages": "486", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "487", "messages": "488", "suppressedMessages": "489", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "490", "messages": "491", "suppressedMessages": "492", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "493", "messages": "494", "suppressedMessages": "495", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "496", "messages": "497", "suppressedMessages": "498", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "499", "messages": "500", "suppressedMessages": "501", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "502", "messages": "503", "suppressedMessages": "504", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "505", "messages": "506", "suppressedMessages": "507", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "508", "messages": "509", "suppressedMessages": "510", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "511", "messages": "512", "suppressedMessages": "513", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "514", "messages": "515", "suppressedMessages": "516", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "517", "messages": "518", "suppressedMessages": "519", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "520", "messages": "521", "suppressedMessages": "522", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "523", "messages": "524", "suppressedMessages": "525", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "526", "messages": "527", "suppressedMessages": "528", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "529", "messages": "530", "suppressedMessages": "531", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "532", "messages": "533", "suppressedMessages": "534", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "535", "messages": "536", "suppressedMessages": "537", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "538", "messages": "539", "suppressedMessages": "540", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "541", "messages": "542", "suppressedMessages": "543", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "544", "messages": "545", "suppressedMessages": "546", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "547", "messages": "548", "suppressedMessages": "549", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "550", "messages": "551", "suppressedMessages": "552", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "553", "messages": "554", "suppressedMessages": "555", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "556", "messages": "557", "suppressedMessages": "558", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "559", "messages": "560", "suppressedMessages": "561", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "562", "messages": "563", "suppressedMessages": "564", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "565", "messages": "566", "suppressedMessages": "567", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "568", "messages": "569", "suppressedMessages": "570", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "571", "messages": "572", "suppressedMessages": "573", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "574", "messages": "575", "suppressedMessages": "576", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "577", "messages": "578", "suppressedMessages": "579", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "580", "messages": "581", "suppressedMessages": "582", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "583", "messages": "584", "suppressedMessages": "585", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "586", "messages": "587", "suppressedMessages": "588", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "589", "messages": "590", "suppressedMessages": "591", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "592", "messages": "593", "suppressedMessages": "594", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "595", "messages": "596", "suppressedMessages": "597", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "598", "messages": "599", "suppressedMessages": "600", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "601", "messages": "602", "suppressedMessages": "603", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "604", "messages": "605", "suppressedMessages": "606", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "607", "messages": "608", "suppressedMessages": "609", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "610", "messages": "611", "suppressedMessages": "612", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "613", "messages": "614", "suppressedMessages": "615", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "616", "messages": "617", "suppressedMessages": "618", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "619", "messages": "620", "suppressedMessages": "621", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "622", "messages": "623", "suppressedMessages": "624", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "625", "messages": "626", "suppressedMessages": "627", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "628", "messages": "629", "suppressedMessages": "630", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "631", "messages": "632", "suppressedMessages": "633", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "634", "messages": "635", "suppressedMessages": "636", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "637", "messages": "638", "suppressedMessages": "639", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "640", "messages": "641", "suppressedMessages": "642", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "643", "messages": "644", "suppressedMessages": "645", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "646", "messages": "647", "suppressedMessages": "648", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "649", "messages": "650", "suppressedMessages": "651", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "652", "messages": "653", "suppressedMessages": "654", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "655", "messages": "656", "suppressedMessages": "657", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "658", "messages": "659", "suppressedMessages": "660", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "661", "messages": "662", "suppressedMessages": "663", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "664", "messages": "665", "suppressedMessages": "666", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "667", "messages": "668", "suppressedMessages": "669", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "670", "messages": "671", "suppressedMessages": "672", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "673", "messages": "674", "suppressedMessages": "675", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "676", "messages": "677", "suppressedMessages": "678", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "679", "messages": "680", "suppressedMessages": "681", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "682", "messages": "683", "suppressedMessages": "684", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "685", "messages": "686", "suppressedMessages": "687", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "688", "messages": "689", "suppressedMessages": "690", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "691", "messages": "692", "suppressedMessages": "693", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "694", "messages": "695", "suppressedMessages": "696", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "697", "messages": "698", "suppressedMessages": "699", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "700", "messages": "701", "suppressedMessages": "702", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "703", "messages": "704", "suppressedMessages": "705", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "706", "messages": "707", "suppressedMessages": "708", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "709", "messages": "710", "suppressedMessages": "711", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\reportWebVitals.ts", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\App.tsx", ["712", "713", "714", "715", "716", "717", "718", "719", "720", "721", "722", "723", "724", "725", "726", "727", "728", "729", "730"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\theme.tsx", ["731"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\store\\index.tsx", ["732"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\context\\toast.context.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\context\\loading.context.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\actions\\auth.actions.tsx", ["733", "734", "735", "736", "737", "738"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\constants\\toastSeverity.constant.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\forgotPassword\\forgotPassword.screen.tsx", ["739"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\signIn\\signIn.screen.tsx", ["740", "741", "742", "743", "744", "745", "746", "747", "748"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\dashboard\\dashboard.screen.tsx", ["749"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\qanda\\qanda.screen.tsx", ["750", "751", "752", "753", "754", "755", "756", "757", "758", "759", "760", "761", "762"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\help\\help.screen.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\analytics\\analytics.screen.tsx", ["763", "764", "765", "766"], ["767"], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\createSocialPost\\createSocialPost.screen.tsx", ["768", "769", "770", "771", "772", "773", "774", "775", "776", "777", "778", "779", "780", "781", "782", "783", "784", "785", "786", "787", "788"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\dashboardV2\\dashboardV2.screen.tsx", ["789", "790", "791", "792", "793", "794", "795", "796", "797", "798", "799", "800", "801", "802", "803", "804", "805", "806", "807", "808", "809", "810"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessCategory\\businessCategory.screen.tsx", ["811"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessCategory\\servicesDemo.screen.tsx", ["812", "813", "814"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessCategory\\demo.screen.tsx", ["815", "816", "817", "818", "819", "820"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\geoGrid\\geoGrid.screen.tsx", ["821", "822", "823", "824", "825"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\userManagement\\roles\\roles.screen.tsx", ["826", "827", "828", "829", "830", "831", "832", "833", "834", "835"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\userManagement\\users\\users.screen.tsx", ["836", "837", "838", "839", "840", "841", "842", "843", "844", "845", "846", "847", "848"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessManagement\\manageBusiness\\manageBusiness.screen.tsx", ["849", "850", "851", "852", "853", "854", "855", "856", "857", "858", "859", "860", "861"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessManagement\\businessSummary\\businessSummary.screen.tsx", ["862", "863", "864", "865", "866", "867", "868", "869", "870", "871", "872", "873", "874", "875", "876", "877", "878"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessManagement\\localBusiness\\localBusiness.screen.tsx", ["879", "880", "881", "882", "883", "884", "885", "886", "887", "888"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessManagement\\callback\\callback.screen.tsx", ["889", "890", "891", "892"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\reviewManagement\\manageReviews\\manageReviews.screen.tsx", ["893", "894", "895", "896", "897", "898", "899", "900", "901", "902", "903", "904", "905", "906", "907", "908", "909", "910", "911", "912", "913", "914", "915", "916", "917", "918", "919", "920", "921"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\reducers\\userPreferences.reducer.tsx", ["922", "923", "924", "925", "926", "927", "928"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\reducers\\auth.reducer.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\posts\\listing\\PostListing.screen.tsx", ["929", "930", "931", "932", "933", "934", "935", "936", "937", "938", "939", "940", "941", "942", "943"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\loader\\loader.component.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\unAuthorized\\notFoundPage.component.tsx", ["944"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\notFoundPage\\notFoundPage.component.tsx", ["945"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\constants\\endPoints.constant.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\constants\\reducer.constant.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\httpHelper.service.tsx", ["946", "947"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\helperService.tsx", ["948", "949"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\constants\\dbConstant.constant.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\constants\\message.constant.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\leftMenu\\leftMenu.component.tsx", ["950", "951", "952", "953", "954", "955", "956", "957", "958", "959", "960", "961", "962", "963", "964", "965", "966", "967"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\ApplicationHelperService.tsx", ["968", "969"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\dashboardV2\\websiteClicksChart.tsx", ["970", "971"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\dashboardV2\\platformBreakdownChart.tsx", ["972"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\charts\\registeredEmployees.charts.tsx", ["973", "974", "975"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\charts\\activeJobs.charts.tsx", ["976"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\charts\\pie.charts.tsx", ["977", "978"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\homeChartCard\\homeChartCard.component.tsx", ["979", "980"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\qanda\\qanda.service.tsx", ["981"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\business\\business.service.tsx", ["982"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\location\\location.service.tsx", ["983"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\replyQuestionAnswer\\replyQuestionAnswer.component.tsx", ["984", "985", "986"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\genericDrawer\\genericDrawer.component.tsx", ["987", "988", "989", "990"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\revenueChartDashboard\\revenueChartDashboard.component.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\constants\\application.constant.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\locationChips\\locationChips.component.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\dateFilter\\dateFilter.component.tsx", ["991"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\locationMetrics\\locationMetrics.service.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\createSocialPost\\components\\InfoCard.screen.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\createSocialPost\\components\\submitPost.component.tsx", ["992", "993", "994", "995", "996", "997", "998", "999", "1000", "1001", "1002", "1003", "1004", "1005", "1006", "1007", "1008", "1009", "1010", "1011", "1012", "1013", "1014", "1015", "1016", "1017", "1018", "1019", "1020", "1021", "1022", "1023", "1024"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\dashboardV2\\searchBreakdown.tsx", ["1025", "1026", "1027", "1028", "1029"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\dashboardV2\\businessProfileInteractionsChart.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\posts\\posts.service.tsx", ["1030", "1031", "1032"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessCategory\\components\\addBusinessCategory.component.tsx", ["1033", "1034", "1035", "1036", "1037", "1038", "1039", "1040", "1041", "1042", "1043", "1044", "1045", "1046", "1047", "1048", "1049", "1050", "1051", "1052"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessCategory\\components\\servicesDisplay.component.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessCategory\\components\\addServices.component.tsx", ["1053"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessCategory\\components\\BusinessHours.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessCategory\\components\\AddSpecialHours.tsx", ["1054", "1055", "1056", "1057", "1058"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\editBusinessName\\editBusinessName.component.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\moreActivity\\FromTheBusiness.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\moreActivity\\AddChildren.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\moreActivity\\MoreAccessibility.tsx", ["1059"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\moreActivity\\Amenities.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\moreActivity\\ServiceOptions.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\moreActivity\\CrowdComponent.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\moreActivity\\ParkingComponent.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\moreActivity\\PaymentsComponent.tsx", ["1060"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\moreActivity\\PlanningComponent.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\geoGrid\\GeoGridControls.component.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\geoGrid\\GeoGridMap.component.tsx", ["1061", "1062"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\geoGrid\\geoGrid.service.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\geoGrid\\GeoGridSettings.component.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\roles\\roles.service.tsx", ["1063", "1064"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\user\\user.service.tsx", ["1065", "1066", "1067"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\confirmModel\\confirmModel.component.tsx", ["1068", "1069"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\addEditUser\\addEditUser.component.tsx", ["1070", "1071", "1072", "1073", "1074", "1075", "1076", "1077", "1078", "1079", "1080", "1081"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\auth\\auth.service.tsx", ["1082", "1083"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\alertDialog\\alertDialog.component.tsx", ["1084"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\noRowsFound\\noRowsFound.component.tsx", ["1085", "1086", "1087"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\logosPhotosDisplay\\logsPhotosDisplay.component.tsx", ["1088", "1089", "1090"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\categoryDisplay\\categoryDisplay.component.tsx", ["1091", "1092", "1093", "1094"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\LinearProgressWithLabel\\LinearProgressWithLabel.component.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\addEditBusiness\\addEditBusiness.component.tsx", ["1095", "1096", "1097", "1098", "1099", "1100", "1101", "1102", "1103", "1104", "1105"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\serviceItemsDisplay\\serviceItemsDisplay.component.tsx", ["1106", "1107", "1108", "1109", "1110"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\regularHoursTable\\regularHoursTable.component.tsx", ["1111", "1112", "1113", "1114", "1115", "1116", "1117", "1118", "1119", "1120", "1121"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\serviceAreaList\\serviceAreaList.component.tsx", ["1122", "1123", "1124"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\iconOnAvailability\\iconOnAvailability.component.tsx", ["1125", "1126", "1127"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\userAvatarWIthName\\userAvatarWIthName.component.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\replyReviews\\replyReviews.component.tsx", ["1128", "1129", "1130", "1131"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\createPost\\createPost.component.tsx", ["1132", "1133", "1134", "1135", "1136", "1137", "1138", "1139", "1140", "1141", "1142", "1143", "1144"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\createTags\\createTags.component.tsx", ["1145", "1146", "1147", "1148", "1149", "1150"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\ratingsStar\\ratingsStar.component.tsx", ["1151", "1152", "1153"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\review\\review.service.tsx", ["1154"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\posts\\listing\\postCard\\postCard.screen.tsx", ["1155", "1156", "1157", "1158", "1159", "1160"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\posts\\updatesSection\\updatesSection.screen.tsx", ["1161", "1162"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\actions\\userPreferences.actions.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\menuListItemNested\\menuListItemNested.component.tsx", ["1163", "1164", "1165"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\exportButton\\exportButton.component.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\header\\header.component.tsx", ["1166", "1167", "1168", "1169", "1170", "1171", "1172", "1173", "1174", "1175"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\mediaGallery\\mediaGallery.component.tsx", ["1176", "1177"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\userAvatar\\userAvatar.component.tsx", ["1178"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\editElements\\editElements.component.tsx", ["1179", "1180", "1181"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\colorPalette\\colorPalette.component.tsx", ["1182", "1183", "1184"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\createPost\\createPostTemplates\\cards\\testimonialCard1\\testimonialCard1.component.tsx", ["1185"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\createPost\\createPostTemplates\\cards\\testimonialCard3\\testimonialCard3.component.tsx", ["1186", "1187", "1188", "1189"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\createPost\\createPostTemplates\\cards\\testimonialCard2\\testimonialCard2.component.tsx", ["1190", "1191", "1192"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\createPost\\createPostTemplates\\cards\\testimonialCard4\\testimonialCard4.component.tsx", ["1193", "1194", "1195", "1196"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\createPost\\createPostTemplates\\cards\\testimonialCard5\\testimonialCard5.component.tsx", ["1197", "1198", "1199", "1200", "1201"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\createPost\\createPostTemplates\\cards\\testimonialCard6\\testimonialCard6.component.tsx", ["1202", "1203", "1204", "1205"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\excelExport.service.ts", ["1206"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\context\\preferences.context.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\utils\\googleMaps.utils.ts", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\reviewManagement\\reviewSettings\\reviewSettings.screen.tsx", ["1207", "1208", "1209", "1210", "1211", "1212", "1213", "1214", "1215", "1216", "1217", "1218", "1219"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\reviewSettings\\reviewSettings.service.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\reviewManagement\\reviewSettings\\components\\autoReplySettings.component.tsx", ["1220", "1221"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\reviewManagement\\reviewSettings\\components\\createEditTemplate.component.tsx", ["1222", "1223"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\manageAssets\\manageAssets.screen.tsx", ["1224", "1225", "1226", "1227", "1228", "1229", "1230", "1231"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\manageAssets\\manageAssets.service.ts", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\fileGallery\\fileGallery.component.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\fileUpload\\fileUpload.component.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\fileViewer\\fileViewer.component.tsx", ["1232"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\confirmDelete\\confirmDelete.component.tsx", ["1233"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\utils\\fileUtils.ts", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\manageAssets\\components\\confirmDelete.component.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\manageAssets\\components\\fileUpload.component.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\manageAssets\\components\\fileViewer.component.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\manageAssets\\components\\fileGallery.component.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessManagement\\instagramCallback\\instagramCallback.screen.tsx", ["1234", "1235"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\InstagramSyncButton\\InstagramSyncButton.tsx", ["1236", "1237"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\instagram\\instagram.service.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\constants\\dbConstant.constant.ts", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\createSocialPost\\components\\gallerySelection.component.tsx", ["1238", "1239", "1240", "1241", "1242"], [], {"ruleId": "1243", "severity": 1, "message": "1244", "line": 3, "column": 3, "nodeType": "1245", "messageId": "1246", "endLine": 3, "endColumn": 13}, {"ruleId": "1243", "severity": 1, "message": "1247", "line": 9, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 9, "endColumn": 12}, {"ruleId": "1243", "severity": 1, "message": "1248", "line": 11, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 11, "endColumn": 14}, {"ruleId": "1243", "severity": 1, "message": "1249", "line": 12, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 12, "endColumn": 17}, {"ruleId": "1243", "severity": 1, "message": "1250", "line": 13, "column": 10, "nodeType": "1245", "messageId": "1246", "endLine": 13, "endColumn": 22}, {"ruleId": "1243", "severity": 1, "message": "1251", "line": 16, "column": 10, "nodeType": "1245", "messageId": "1246", "endLine": 16, "endColumn": 28}, {"ruleId": "1243", "severity": 1, "message": "1252", "line": 19, "column": 10, "nodeType": "1245", "messageId": "1246", "endLine": 19, "endColumn": 15}, {"ruleId": "1243", "severity": 1, "message": "1253", "line": 62, "column": 10, "nodeType": "1245", "messageId": "1246", "endLine": 62, "endColumn": 24}, {"ruleId": "1243", "severity": 1, "message": "1254", "line": 62, "column": 26, "nodeType": "1245", "messageId": "1246", "endLine": 62, "endColumn": 43}, {"ruleId": "1255", "severity": 1, "message": "1256", "line": 78, "column": 5, "nodeType": "1257", "endLine": 78, "endColumn": 14, "suggestions": "1258"}, {"ruleId": "1255", "severity": 1, "message": "1259", "line": 86, "column": 5, "nodeType": "1257", "endLine": 86, "endColumn": 14, "suggestions": "1260"}, {"ruleId": "1243", "severity": 1, "message": "1261", "line": 97, "column": 9, "nodeType": "1245", "messageId": "1246", "endLine": 97, "endColumn": 26}, {"ruleId": "1255", "severity": 1, "message": "1262", "line": 101, "column": 5, "nodeType": "1257", "endLine": 101, "endColumn": 21, "suggestions": "1263"}, {"ruleId": "1255", "severity": 1, "message": "1264", "line": 129, "column": 6, "nodeType": "1257", "endLine": 129, "endColumn": 25, "suggestions": "1265"}, {"ruleId": "1255", "severity": 1, "message": "1266", "line": 140, "column": 6, "nodeType": "1257", "endLine": 140, "endColumn": 22, "suggestions": "1267"}, {"ruleId": "1255", "severity": 1, "message": "1268", "line": 164, "column": 5, "nodeType": "1257", "endLine": 164, "endColumn": 11, "suggestions": "1269"}, {"ruleId": "1255", "severity": 1, "message": "1268", "line": 171, "column": 5, "nodeType": "1257", "endLine": 171, "endColumn": 11, "suggestions": "1270"}, {"ruleId": "1255", "severity": 1, "message": "1271", "line": 178, "column": 5, "nodeType": "1257", "endLine": 178, "endColumn": 18, "suggestions": "1272"}, {"ruleId": "1255", "severity": 1, "message": "1273", "line": 190, "column": 5, "nodeType": "1257", "endLine": 190, "endColumn": 33, "suggestions": "1274"}, {"ruleId": "1243", "severity": 1, "message": "1275", "line": 3, "column": 24, "nodeType": "1245", "messageId": "1246", "endLine": 3, "endColumn": 38}, {"ruleId": "1243", "severity": 1, "message": "1276", "line": 6, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 6, "endColumn": 21}, {"ruleId": "1243", "severity": 1, "message": "1277", "line": 1, "column": 10, "nodeType": "1245", "messageId": "1246", "endLine": 1, "endColumn": 15}, {"ruleId": "1243", "severity": 1, "message": "1244", "line": 4, "column": 10, "nodeType": "1245", "messageId": "1246", "endLine": 4, "endColumn": 20}, {"ruleId": "1243", "severity": 1, "message": "1278", "line": 4, "column": 22, "nodeType": "1245", "messageId": "1246", "endLine": 4, "endColumn": 31}, {"ruleId": "1243", "severity": 1, "message": "1279", "line": 12, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 12, "endColumn": 13}, {"ruleId": "1243", "severity": 1, "message": "1280", "line": 14, "column": 10, "nodeType": "1245", "messageId": "1246", "endLine": 14, "endColumn": 19}, {"ruleId": "1243", "severity": 1, "message": "1281", "line": 18, "column": 7, "nodeType": "1245", "messageId": "1246", "endLine": 18, "endColumn": 32}, {"ruleId": "1243", "severity": 1, "message": "1282", "line": 11, "column": 13, "nodeType": "1245", "messageId": "1246", "endLine": 11, "endColumn": 16}, {"ruleId": "1243", "severity": 1, "message": "1283", "line": 12, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 12, "endColumn": 12}, {"ruleId": "1243", "severity": 1, "message": "1284", "line": 16, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 16, "endColumn": 19}, {"ruleId": "1243", "severity": 1, "message": "1285", "line": 20, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 20, "endColumn": 19}, {"ruleId": "1243", "severity": 1, "message": "1286", "line": 21, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 21, "endColumn": 18}, {"ruleId": "1243", "severity": 1, "message": "1287", "line": 38, "column": 10, "nodeType": "1245", "messageId": "1246", "endLine": 38, "endColumn": 26}, {"ruleId": "1243", "severity": 1, "message": "1288", "line": 42, "column": 27, "nodeType": "1245", "messageId": "1246", "endLine": 42, "endColumn": 34}, {"ruleId": "1243", "severity": 1, "message": "1289", "line": 45, "column": 9, "nodeType": "1245", "messageId": "1246", "endLine": 45, "endColumn": 32}, {"ruleId": "1243", "severity": 1, "message": "1290", "line": 51, "column": 9, "nodeType": "1245", "messageId": "1246", "endLine": 51, "endColumn": 30}, {"ruleId": "1255", "severity": 1, "message": "1291", "line": 79, "column": 6, "nodeType": "1257", "endLine": 79, "endColumn": 8, "suggestions": "1292"}, {"ruleId": "1243", "severity": 1, "message": "1293", "line": 27, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 27, "endColumn": 29}, {"ruleId": "1243", "severity": 1, "message": "1294", "line": 17, "column": 3, "nodeType": "1245", "messageId": "1246", "endLine": 17, "endColumn": 10}, {"ruleId": "1243", "severity": 1, "message": "1295", "line": 18, "column": 3, "nodeType": "1245", "messageId": "1246", "endLine": 18, "endColumn": 9}, {"ruleId": "1243", "severity": 1, "message": "1296", "line": 64, "column": 10, "nodeType": "1245", "messageId": "1246", "endLine": 64, "endColumn": 19}, {"ruleId": "1243", "severity": 1, "message": "1297", "line": 64, "column": 21, "nodeType": "1245", "messageId": "1246", "endLine": 64, "endColumn": 33}, {"ruleId": "1243", "severity": 1, "message": "1298", "line": 67, "column": 9, "nodeType": "1245", "messageId": "1246", "endLine": 67, "endColumn": 25}, {"ruleId": "1243", "severity": 1, "message": "1281", "line": 69, "column": 9, "nodeType": "1245", "messageId": "1246", "endLine": 69, "endColumn": 34}, {"ruleId": "1243", "severity": 1, "message": "1288", "line": 71, "column": 27, "nodeType": "1245", "messageId": "1246", "endLine": 71, "endColumn": 34}, {"ruleId": "1243", "severity": 1, "message": "1299", "line": 73, "column": 10, "nodeType": "1245", "messageId": "1246", "endLine": 73, "endColumn": 34}, {"ruleId": "1243", "severity": 1, "message": "1300", "line": 81, "column": 10, "nodeType": "1245", "messageId": "1246", "endLine": 81, "endColumn": 25}, {"ruleId": "1243", "severity": 1, "message": "1301", "line": 95, "column": 25, "nodeType": "1245", "messageId": "1246", "endLine": 95, "endColumn": 41}, {"ruleId": "1302", "severity": 1, "message": "1303", "line": 134, "column": 56, "nodeType": "1304", "messageId": "1305", "endLine": 134, "endColumn": 58}, {"ruleId": "1302", "severity": 1, "message": "1303", "line": 139, "column": 57, "nodeType": "1304", "messageId": "1305", "endLine": 139, "endColumn": 59}, {"ruleId": "1255", "severity": 1, "message": "1306", "line": 146, "column": 6, "nodeType": "1257", "endLine": 146, "endColumn": 8, "suggestions": "1307"}, {"ruleId": "1243", "severity": 1, "message": "1294", "line": 7, "column": 10, "nodeType": "1245", "messageId": "1246", "endLine": 7, "endColumn": 17}, {"ruleId": "1243", "severity": 1, "message": "1308", "line": 13, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 13, "endColumn": 21}, {"ruleId": "1243", "severity": 1, "message": "1309", "line": 140, "column": 10, "nodeType": "1245", "messageId": "1246", "endLine": 140, "endColumn": 30}, {"ruleId": "1302", "severity": 1, "message": "1303", "line": 306, "column": 56, "nodeType": "1304", "messageId": "1305", "endLine": 306, "endColumn": 58}, {"ruleId": "1255", "severity": 1, "message": "1310", "line": 151, "column": 6, "nodeType": "1257", "endLine": 151, "endColumn": 8, "suggestions": "1311", "suppressions": "1312"}, {"ruleId": "1243", "severity": 1, "message": "1313", "line": 8, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 8, "endColumn": 17}, {"ruleId": "1243", "severity": 1, "message": "1314", "line": 38, "column": 10, "nodeType": "1245", "messageId": "1246", "endLine": 38, "endColumn": 21}, {"ruleId": "1243", "severity": 1, "message": "1315", "line": 40, "column": 10, "nodeType": "1245", "messageId": "1246", "endLine": 40, "endColumn": 22}, {"ruleId": "1243", "severity": 1, "message": "1316", "line": 62, "column": 10, "nodeType": "1245", "messageId": "1246", "endLine": 62, "endColumn": 23}, {"ruleId": "1243", "severity": 1, "message": "1317", "line": 89, "column": 10, "nodeType": "1245", "messageId": "1246", "endLine": 89, "endColumn": 34}, {"ruleId": "1243", "severity": 1, "message": "1318", "line": 100, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 100, "endColumn": 21}, {"ruleId": "1243", "severity": 1, "message": "1319", "line": 101, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 101, "endColumn": 21}, {"ruleId": "1243", "severity": 1, "message": "1320", "line": 237, "column": 10, "nodeType": "1245", "messageId": "1246", "endLine": 237, "endColumn": 14}, {"ruleId": "1243", "severity": 1, "message": "1321", "line": 237, "column": 16, "nodeType": "1245", "messageId": "1246", "endLine": 237, "endColumn": 23}, {"ruleId": "1243", "severity": 1, "message": "1322", "line": 238, "column": 10, "nodeType": "1245", "messageId": "1246", "endLine": 238, "endColumn": 26}, {"ruleId": "1243", "severity": 1, "message": "1323", "line": 238, "column": 28, "nodeType": "1245", "messageId": "1246", "endLine": 238, "endColumn": 47}, {"ruleId": "1243", "severity": 1, "message": "1324", "line": 253, "column": 10, "nodeType": "1245", "messageId": "1246", "endLine": 253, "endColumn": 29}, {"ruleId": "1255", "severity": 1, "message": "1325", "line": 367, "column": 6, "nodeType": "1257", "endLine": 367, "endColumn": 8, "suggestions": "1326"}, {"ruleId": "1243", "severity": 1, "message": "1327", "line": 369, "column": 9, "nodeType": "1245", "messageId": "1246", "endLine": 369, "endColumn": 26}, {"ruleId": "1243", "severity": 1, "message": "1328", "line": 371, "column": 11, "nodeType": "1245", "messageId": "1246", "endLine": 371, "endColumn": 18}, {"ruleId": "1243", "severity": 1, "message": "1329", "line": 651, "column": 11, "nodeType": "1245", "messageId": "1246", "endLine": 651, "endColumn": 19}, {"ruleId": "1243", "severity": 1, "message": "1330", "line": 1009, "column": 11, "nodeType": "1245", "messageId": "1246", "endLine": 1009, "endColumn": 27}, {"ruleId": "1302", "severity": 1, "message": "1331", "line": 1610, "column": 71, "nodeType": "1304", "messageId": "1305", "endLine": 1610, "endColumn": 73}, {"ruleId": "1302", "severity": 1, "message": "1303", "line": 1625, "column": 42, "nodeType": "1304", "messageId": "1305", "endLine": 1625, "endColumn": 44}, {"ruleId": "1332", "severity": 1, "message": "1333", "line": 1856, "column": 19, "nodeType": "1334", "endLine": 1867, "endColumn": 21}, {"ruleId": "1332", "severity": 1, "message": "1333", "line": 1914, "column": 17, "nodeType": "1334", "endLine": 1942, "endColumn": 19}, {"ruleId": "1243", "severity": 1, "message": "1335", "line": 9, "column": 10, "nodeType": "1245", "messageId": "1246", "endLine": 9, "endColumn": 34}, {"ruleId": "1243", "severity": 1, "message": "1336", "line": 10, "column": 10, "nodeType": "1245", "messageId": "1246", "endLine": 10, "endColumn": 25}, {"ruleId": "1243", "severity": 1, "message": "1337", "line": 11, "column": 10, "nodeType": "1245", "messageId": "1246", "endLine": 11, "endColumn": 18}, {"ruleId": "1243", "severity": 1, "message": "1338", "line": 14, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 14, "endColumn": 17}, {"ruleId": "1243", "severity": 1, "message": "1339", "line": 15, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 15, "endColumn": 23}, {"ruleId": "1243", "severity": 1, "message": "1340", "line": 16, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 16, "endColumn": 26}, {"ruleId": "1243", "severity": 1, "message": "1341", "line": 17, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 17, "endColumn": 20}, {"ruleId": "1243", "severity": 1, "message": "1342", "line": 18, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 18, "endColumn": 27}, {"ruleId": "1243", "severity": 1, "message": "1343", "line": 19, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 19, "endColumn": 16}, {"ruleId": "1243", "severity": 1, "message": "1344", "line": 21, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 21, "endColumn": 30}, {"ruleId": "1243", "severity": 1, "message": "1345", "line": 22, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 22, "endColumn": 32}, {"ruleId": "1243", "severity": 1, "message": "1308", "line": 26, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 26, "endColumn": 21}, {"ruleId": "1243", "severity": 1, "message": "1346", "line": 31, "column": 10, "nodeType": "1245", "messageId": "1246", "endLine": 31, "endColumn": 24}, {"ruleId": "1243", "severity": 1, "message": "1347", "line": 31, "column": 26, "nodeType": "1245", "messageId": "1246", "endLine": 31, "endColumn": 31}, {"ruleId": "1243", "severity": 1, "message": "1248", "line": 40, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 40, "endColumn": 14}, {"ruleId": "1243", "severity": 1, "message": "1348", "line": 41, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 41, "endColumn": 33}, {"ruleId": "1243", "severity": 1, "message": "1349", "line": 43, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 43, "endColumn": 25}, {"ruleId": "1243", "severity": 1, "message": "1350", "line": 66, "column": 21, "nodeType": "1245", "messageId": "1246", "endLine": 66, "endColumn": 29}, {"ruleId": "1243", "severity": 1, "message": "1288", "line": 72, "column": 27, "nodeType": "1245", "messageId": "1246", "endLine": 72, "endColumn": 34}, {"ruleId": "1255", "severity": 1, "message": "1351", "line": 116, "column": 6, "nodeType": "1257", "endLine": 116, "endColumn": 8, "suggestions": "1352"}, {"ruleId": "1255", "severity": 1, "message": "1353", "line": 125, "column": 6, "nodeType": "1257", "endLine": 125, "endColumn": 26, "suggestions": "1354"}, {"ruleId": "1255", "severity": 1, "message": "1355", "line": 138, "column": 6, "nodeType": "1257", "endLine": 138, "endColumn": 45, "suggestions": "1356"}, {"ruleId": "1243", "severity": 1, "message": "1357", "line": 9, "column": 3, "nodeType": "1245", "messageId": "1246", "endLine": 9, "endColumn": 13}, {"ruleId": "1243", "severity": 1, "message": "1357", "line": 9, "column": 3, "nodeType": "1245", "messageId": "1246", "endLine": 9, "endColumn": 13}, {"ruleId": "1243", "severity": 1, "message": "1358", "line": 10, "column": 3, "nodeType": "1245", "messageId": "1246", "endLine": 10, "endColumn": 9}, {"ruleId": "1243", "severity": 1, "message": "1359", "line": 13, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 13, "endColumn": 15}, {"ruleId": "1243", "severity": 1, "message": "1360", "line": 25, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 25, "endColumn": 23}, {"ruleId": "1243", "severity": 1, "message": "1361", "line": 33, "column": 10, "nodeType": "1245", "messageId": "1246", "endLine": 33, "endColumn": 23}, {"ruleId": "1243", "severity": 1, "message": "1362", "line": 33, "column": 33, "nodeType": "1245", "messageId": "1246", "endLine": 33, "endColumn": 41}, {"ruleId": "1243", "severity": 1, "message": "1363", "line": 34, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 34, "endColumn": 16}, {"ruleId": "1243", "severity": 1, "message": "1364", "line": 36, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 36, "endColumn": 20}, {"ruleId": "1243", "severity": 1, "message": "1365", "line": 37, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 37, "endColumn": 22}, {"ruleId": "1243", "severity": 1, "message": "1366", "line": 1, "column": 38, "nodeType": "1245", "messageId": "1246", "endLine": 1, "endColumn": 49}, {"ruleId": "1243", "severity": 1, "message": "1367", "line": 4, "column": 3, "nodeType": "1245", "messageId": "1246", "endLine": 4, "endColumn": 12}, {"ruleId": "1243", "severity": 1, "message": "1368", "line": 67, "column": 10, "nodeType": "1245", "messageId": "1246", "endLine": 67, "endColumn": 19}, {"ruleId": "1243", "severity": 1, "message": "1369", "line": 67, "column": 21, "nodeType": "1245", "messageId": "1246", "endLine": 67, "endColumn": 33}, {"ruleId": "1255", "severity": 1, "message": "1370", "line": 79, "column": 6, "nodeType": "1257", "endLine": 79, "endColumn": 19, "suggestions": "1371"}, {"ruleId": "1243", "severity": 1, "message": "1372", "line": 14, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 14, "endColumn": 13}, {"ruleId": "1243", "severity": 1, "message": "1373", "line": 25, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 25, "endColumn": 18}, {"ruleId": "1243", "severity": 1, "message": "1374", "line": 26, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 26, "endColumn": 16}, {"ruleId": "1243", "severity": 1, "message": "1375", "line": 31, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 31, "endColumn": 25}, {"ruleId": "1243", "severity": 1, "message": "1295", "line": 47, "column": 10, "nodeType": "1245", "messageId": "1246", "endLine": 47, "endColumn": 16}, {"ruleId": "1243", "severity": 1, "message": "1252", "line": 53, "column": 10, "nodeType": "1245", "messageId": "1246", "endLine": 53, "endColumn": 15}, {"ruleId": "1243", "severity": 1, "message": "1288", "line": 70, "column": 27, "nodeType": "1245", "messageId": "1246", "endLine": 70, "endColumn": 34}, {"ruleId": "1255", "severity": 1, "message": "1376", "line": 104, "column": 6, "nodeType": "1257", "endLine": 104, "endColumn": 8, "suggestions": "1377"}, {"ruleId": "1255", "severity": 1, "message": "1378", "line": 108, "column": 6, "nodeType": "1257", "endLine": 108, "endColumn": 8, "suggestions": "1379"}, {"ruleId": "1243", "severity": 1, "message": "1329", "line": 223, "column": 13, "nodeType": "1245", "messageId": "1246", "endLine": 223, "endColumn": 21}, {"ruleId": "1243", "severity": 1, "message": "1380", "line": 20, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 20, "endColumn": 21}, {"ruleId": "1243", "severity": 1, "message": "1381", "line": 21, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 21, "endColumn": 28}, {"ruleId": "1243", "severity": 1, "message": "1382", "line": 34, "column": 3, "nodeType": "1245", "messageId": "1246", "endLine": 34, "endColumn": 21}, {"ruleId": "1243", "severity": 1, "message": "1295", "line": 49, "column": 10, "nodeType": "1245", "messageId": "1246", "endLine": 49, "endColumn": 16}, {"ruleId": "1243", "severity": 1, "message": "1383", "line": 49, "column": 18, "nodeType": "1245", "messageId": "1246", "endLine": 49, "endColumn": 22}, {"ruleId": "1243", "severity": 1, "message": "1384", "line": 49, "column": 31, "nodeType": "1245", "messageId": "1246", "endLine": 49, "endColumn": 36}, {"ruleId": "1243", "severity": 1, "message": "1385", "line": 53, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 53, "endColumn": 23}, {"ruleId": "1243", "severity": 1, "message": "1288", "line": 91, "column": 27, "nodeType": "1245", "messageId": "1246", "endLine": 91, "endColumn": 34}, {"ruleId": "1243", "severity": 1, "message": "1386", "line": 96, "column": 10, "nodeType": "1245", "messageId": "1246", "endLine": 96, "endColumn": 20}, {"ruleId": "1255", "severity": 1, "message": "1387", "line": 120, "column": 6, "nodeType": "1257", "endLine": 120, "endColumn": 23, "suggestions": "1388"}, {"ruleId": "1243", "severity": 1, "message": "1389", "line": 217, "column": 10, "nodeType": "1245", "messageId": "1246", "endLine": 217, "endColumn": 21}, {"ruleId": "1243", "severity": 1, "message": "1390", "line": 219, "column": 9, "nodeType": "1245", "messageId": "1246", "endLine": 219, "endColumn": 25}, {"ruleId": "1243", "severity": 1, "message": "1391", "line": 224, "column": 9, "nodeType": "1245", "messageId": "1246", "endLine": 224, "endColumn": 32}, {"ruleId": "1243", "severity": 1, "message": "1295", "line": 18, "column": 10, "nodeType": "1245", "messageId": "1246", "endLine": 18, "endColumn": 16}, {"ruleId": "1243", "severity": 1, "message": "1392", "line": 21, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 21, "endColumn": 25}, {"ruleId": "1243", "severity": 1, "message": "1393", "line": 22, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 22, "endColumn": 30}, {"ruleId": "1243", "severity": 1, "message": "1394", "line": 30, "column": 3, "nodeType": "1245", "messageId": "1246", "endLine": 30, "endColumn": 29}, {"ruleId": "1243", "severity": 1, "message": "1395", "line": 44, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 44, "endColumn": 17}, {"ruleId": "1243", "severity": 1, "message": "1381", "line": 51, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 51, "endColumn": 28}, {"ruleId": "1243", "severity": 1, "message": "1396", "line": 59, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 59, "endColumn": 20}, {"ruleId": "1243", "severity": 1, "message": "1397", "line": 61, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 61, "endColumn": 29}, {"ruleId": "1243", "severity": 1, "message": "1398", "line": 62, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 62, "endColumn": 23}, {"ruleId": "1243", "severity": 1, "message": "1288", "line": 87, "column": 27, "nodeType": "1245", "messageId": "1246", "endLine": 87, "endColumn": 34}, {"ruleId": "1243", "severity": 1, "message": "1399", "line": 99, "column": 23, "nodeType": "1245", "messageId": "1246", "endLine": 99, "endColumn": 37}, {"ruleId": "1243", "severity": 1, "message": "1386", "line": 103, "column": 10, "nodeType": "1245", "messageId": "1246", "endLine": 103, "endColumn": 20}, {"ruleId": "1255", "severity": 1, "message": "1400", "line": 109, "column": 6, "nodeType": "1257", "endLine": 109, "endColumn": 8, "suggestions": "1401"}, {"ruleId": "1243", "severity": 1, "message": "1402", "line": 17, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 17, "endColumn": 12}, {"ruleId": "1243", "severity": 1, "message": "1403", "line": 18, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 18, "endColumn": 16}, {"ruleId": "1243", "severity": 1, "message": "1404", "line": 19, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 19, "endColumn": 14}, {"ruleId": "1243", "severity": 1, "message": "1405", "line": 29, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 29, "endColumn": 23}, {"ruleId": "1243", "severity": 1, "message": "1406", "line": 40, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 40, "endColumn": 20}, {"ruleId": "1243", "severity": 1, "message": "1407", "line": 45, "column": 23, "nodeType": "1245", "messageId": "1246", "endLine": 45, "endColumn": 34}, {"ruleId": "1243", "severity": 1, "message": "1398", "line": 56, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 56, "endColumn": 23}, {"ruleId": "1243", "severity": 1, "message": "1408", "line": 57, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 57, "endColumn": 26}, {"ruleId": "1243", "severity": 1, "message": "1409", "line": 64, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 64, "endColumn": 27}, {"ruleId": "1243", "severity": 1, "message": "1410", "line": 70, "column": 7, "nodeType": "1245", "messageId": "1246", "endLine": 70, "endColumn": 12}, {"ruleId": "1243", "severity": 1, "message": "1411", "line": 72, "column": 11, "nodeType": "1245", "messageId": "1246", "endLine": 72, "endColumn": 26}, {"ruleId": "1243", "severity": 1, "message": "1288", "line": 87, "column": 27, "nodeType": "1245", "messageId": "1246", "endLine": 87, "endColumn": 34}, {"ruleId": "1243", "severity": 1, "message": "1412", "line": 107, "column": 10, "nodeType": "1245", "messageId": "1246", "endLine": 107, "endColumn": 18}, {"ruleId": "1243", "severity": 1, "message": "1413", "line": 107, "column": 20, "nodeType": "1245", "messageId": "1246", "endLine": 107, "endColumn": 31}, {"ruleId": "1255", "severity": 1, "message": "1414", "line": 121, "column": 6, "nodeType": "1257", "endLine": 121, "endColumn": 8, "suggestions": "1415"}, {"ruleId": "1255", "severity": 1, "message": "1416", "line": 223, "column": 6, "nodeType": "1257", "endLine": 223, "endColumn": 23, "suggestions": "1417"}, {"ruleId": "1332", "severity": 1, "message": "1333", "line": 678, "column": 31, "nodeType": "1334", "endLine": 682, "endColumn": 33}, {"ruleId": "1243", "severity": 1, "message": "1295", "line": 20, "column": 3, "nodeType": "1245", "messageId": "1246", "endLine": 20, "endColumn": 9}, {"ruleId": "1243", "severity": 1, "message": "1418", "line": 67, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 67, "endColumn": 17}, {"ruleId": "1243", "severity": 1, "message": "1419", "line": 97, "column": 10, "nodeType": "1245", "messageId": "1246", "endLine": 97, "endColumn": 26}, {"ruleId": "1243", "severity": 1, "message": "1420", "line": 97, "column": 28, "nodeType": "1245", "messageId": "1246", "endLine": 97, "endColumn": 47}, {"ruleId": "1243", "severity": 1, "message": "1288", "line": 102, "column": 27, "nodeType": "1245", "messageId": "1246", "endLine": 102, "endColumn": 34}, {"ruleId": "1243", "severity": 1, "message": "1350", "line": 106, "column": 21, "nodeType": "1245", "messageId": "1246", "endLine": 106, "endColumn": 29}, {"ruleId": "1243", "severity": 1, "message": "1399", "line": 119, "column": 23, "nodeType": "1245", "messageId": "1246", "endLine": 119, "endColumn": 37}, {"ruleId": "1255", "severity": 1, "message": "1291", "line": 133, "column": 6, "nodeType": "1257", "endLine": 133, "endColumn": 8, "suggestions": "1421"}, {"ruleId": "1255", "severity": 1, "message": "1422", "line": 137, "column": 6, "nodeType": "1257", "endLine": 137, "endColumn": 23, "suggestions": "1423"}, {"ruleId": "1255", "severity": 1, "message": "1424", "line": 146, "column": 6, "nodeType": "1257", "endLine": 146, "endColumn": 8, "suggestions": "1425"}, {"ruleId": "1243", "severity": 1, "message": "1426", "line": 12, "column": 3, "nodeType": "1245", "messageId": "1246", "endLine": 12, "endColumn": 15}, {"ruleId": "1243", "severity": 1, "message": "1427", "line": 13, "column": 3, "nodeType": "1245", "messageId": "1246", "endLine": 13, "endColumn": 16}, {"ruleId": "1243", "severity": 1, "message": "1428", "line": 19, "column": 6, "nodeType": "1245", "messageId": "1246", "endLine": 19, "endColumn": 19}, {"ruleId": "1255", "severity": 1, "message": "1429", "line": 57, "column": 6, "nodeType": "1257", "endLine": 57, "endColumn": 20, "suggestions": "1430"}, {"ruleId": "1243", "severity": 1, "message": "1431", "line": 27, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 27, "endColumn": 22}, {"ruleId": "1243", "severity": 1, "message": "1374", "line": 28, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 28, "endColumn": 16}, {"ruleId": "1243", "severity": 1, "message": "1432", "line": 32, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 32, "endColumn": 14}, {"ruleId": "1243", "severity": 1, "message": "1433", "line": 41, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 41, "endColumn": 23}, {"ruleId": "1243", "severity": 1, "message": "1434", "line": 53, "column": 10, "nodeType": "1245", "messageId": "1246", "endLine": 53, "endColumn": 35}, {"ruleId": "1243", "severity": 1, "message": "1435", "line": 57, "column": 3, "nodeType": "1245", "messageId": "1246", "endLine": 57, "endColumn": 16}, {"ruleId": "1243", "severity": 1, "message": "1436", "line": 80, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 80, "endColumn": 18}, {"ruleId": "1243", "severity": 1, "message": "1437", "line": 84, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 84, "endColumn": 12}, {"ruleId": "1243", "severity": 1, "message": "1438", "line": 90, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 90, "endColumn": 26}, {"ruleId": "1243", "severity": 1, "message": "1357", "line": 91, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 91, "endColumn": 18}, {"ruleId": "1243", "severity": 1, "message": "1439", "line": 92, "column": 10, "nodeType": "1245", "messageId": "1246", "endLine": 92, "endColumn": 24}, {"ruleId": "1243", "severity": 1, "message": "1440", "line": 106, "column": 9, "nodeType": "1245", "messageId": "1246", "endLine": 106, "endColumn": 19}, {"ruleId": "1243", "severity": 1, "message": "1441", "line": 107, "column": 9, "nodeType": "1245", "messageId": "1246", "endLine": 107, "endColumn": 19}, {"ruleId": "1243", "severity": 1, "message": "1442", "line": 108, "column": 9, "nodeType": "1245", "messageId": "1246", "endLine": 108, "endColumn": 23}, {"ruleId": "1243", "severity": 1, "message": "1443", "line": 109, "column": 9, "nodeType": "1245", "messageId": "1246", "endLine": 109, "endColumn": 22}, {"ruleId": "1243", "severity": 1, "message": "1298", "line": 114, "column": 9, "nodeType": "1245", "messageId": "1246", "endLine": 114, "endColumn": 25}, {"ruleId": "1243", "severity": 1, "message": "1288", "line": 118, "column": 27, "nodeType": "1245", "messageId": "1246", "endLine": 118, "endColumn": 34}, {"ruleId": "1243", "severity": 1, "message": "1300", "line": 123, "column": 10, "nodeType": "1245", "messageId": "1246", "endLine": 123, "endColumn": 25}, {"ruleId": "1243", "severity": 1, "message": "1444", "line": 128, "column": 10, "nodeType": "1245", "messageId": "1246", "endLine": 128, "endColumn": 20}, {"ruleId": "1243", "severity": 1, "message": "1301", "line": 139, "column": 25, "nodeType": "1245", "messageId": "1246", "endLine": 139, "endColumn": 41}, {"ruleId": "1243", "severity": 1, "message": "1386", "line": 141, "column": 10, "nodeType": "1245", "messageId": "1246", "endLine": 141, "endColumn": 20}, {"ruleId": "1302", "severity": 1, "message": "1303", "line": 179, "column": 56, "nodeType": "1304", "messageId": "1305", "endLine": 179, "endColumn": 58}, {"ruleId": "1302", "severity": 1, "message": "1303", "line": 184, "column": 57, "nodeType": "1304", "messageId": "1305", "endLine": 184, "endColumn": 59}, {"ruleId": "1255", "severity": 1, "message": "1445", "line": 192, "column": 6, "nodeType": "1257", "endLine": 192, "endColumn": 8, "suggestions": "1446"}, {"ruleId": "1243", "severity": 1, "message": "1447", "line": 207, "column": 9, "nodeType": "1245", "messageId": "1246", "endLine": 207, "endColumn": 20}, {"ruleId": "1302", "severity": 1, "message": "1331", "line": 217, "column": 36, "nodeType": "1304", "messageId": "1305", "endLine": 217, "endColumn": 38}, {"ruleId": "1243", "severity": 1, "message": "1448", "line": 278, "column": 9, "nodeType": "1245", "messageId": "1246", "endLine": 278, "endColumn": 20}, {"ruleId": "1302", "severity": 1, "message": "1303", "line": 390, "column": 60, "nodeType": "1304", "messageId": "1305", "endLine": 390, "endColumn": 62}, {"ruleId": "1243", "severity": 1, "message": "1449", "line": 499, "column": 9, "nodeType": "1245", "messageId": "1246", "endLine": 499, "endColumn": 21}, {"ruleId": "1243", "severity": 1, "message": "1450", "line": 2, "column": 3, "nodeType": "1245", "messageId": "1246", "endLine": 2, "endColumn": 17}, {"ruleId": "1243", "severity": 1, "message": "1451", "line": 3, "column": 3, "nodeType": "1245", "messageId": "1246", "endLine": 3, "endColumn": 15}, {"ruleId": "1243", "severity": 1, "message": "1452", "line": 4, "column": 3, "nodeType": "1245", "messageId": "1246", "endLine": 4, "endColumn": 14}, {"ruleId": "1243", "severity": 1, "message": "1453", "line": 5, "column": 3, "nodeType": "1245", "messageId": "1246", "endLine": 5, "endColumn": 13}, {"ruleId": "1243", "severity": 1, "message": "1454", "line": 6, "column": 3, "nodeType": "1245", "messageId": "1246", "endLine": 6, "endColumn": 20}, {"ruleId": "1243", "severity": 1, "message": "1455", "line": 10, "column": 10, "nodeType": "1245", "messageId": "1246", "endLine": 10, "endColumn": 39}, {"ruleId": "1243", "severity": 1, "message": "1456", "line": 11, "column": 10, "nodeType": "1245", "messageId": "1246", "endLine": 11, "endColumn": 36}, {"ruleId": "1243", "severity": 1, "message": "1457", "line": 7, "column": 3, "nodeType": "1245", "messageId": "1246", "endLine": 7, "endColumn": 7}, {"ruleId": "1243", "severity": 1, "message": "1458", "line": 8, "column": 3, "nodeType": "1245", "messageId": "1246", "endLine": 8, "endColumn": 14}, {"ruleId": "1243", "severity": 1, "message": "1418", "line": 11, "column": 3, "nodeType": "1245", "messageId": "1246", "endLine": 11, "endColumn": 12}, {"ruleId": "1243", "severity": 1, "message": "1347", "line": 14, "column": 19, "nodeType": "1245", "messageId": "1246", "endLine": 14, "endColumn": 24}, {"ruleId": "1243", "severity": 1, "message": "1434", "line": 28, "column": 3, "nodeType": "1245", "messageId": "1246", "endLine": 28, "endColumn": 28}, {"ruleId": "1243", "severity": 1, "message": "1295", "line": 32, "column": 3, "nodeType": "1245", "messageId": "1246", "endLine": 32, "endColumn": 9}, {"ruleId": "1243", "severity": 1, "message": "1459", "line": 36, "column": 3, "nodeType": "1245", "messageId": "1246", "endLine": 36, "endColumn": 13}, {"ruleId": "1243", "severity": 1, "message": "1460", "line": 37, "column": 3, "nodeType": "1245", "messageId": "1246", "endLine": 37, "endColumn": 8}, {"ruleId": "1243", "severity": 1, "message": "1461", "line": 51, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 51, "endColumn": 21}, {"ruleId": "1243", "severity": 1, "message": "1462", "line": 57, "column": 10, "nodeType": "1245", "messageId": "1246", "endLine": 57, "endColumn": 15}, {"ruleId": "1243", "severity": 1, "message": "1301", "line": 78, "column": 25, "nodeType": "1245", "messageId": "1246", "endLine": 78, "endColumn": 41}, {"ruleId": "1243", "severity": 1, "message": "1288", "line": 83, "column": 27, "nodeType": "1245", "messageId": "1246", "endLine": 83, "endColumn": 34}, {"ruleId": "1255", "severity": 1, "message": "1463", "line": 118, "column": 6, "nodeType": "1257", "endLine": 118, "endColumn": 8, "suggestions": "1464"}, {"ruleId": "1243", "severity": 1, "message": "1465", "line": 179, "column": 9, "nodeType": "1245", "messageId": "1246", "endLine": 179, "endColumn": 18}, {"ruleId": "1302", "severity": 1, "message": "1331", "line": 368, "column": 65, "nodeType": "1304", "messageId": "1305", "endLine": 368, "endColumn": 67}, {"ruleId": "1255", "severity": 1, "message": "1466", "line": 28, "column": 6, "nodeType": "1257", "endLine": 28, "endColumn": 16, "suggestions": "1467"}, {"ruleId": "1255", "severity": 1, "message": "1466", "line": 28, "column": 6, "nodeType": "1257", "endLine": 28, "endColumn": 16, "suggestions": "1468"}, {"ruleId": "1243", "severity": 1, "message": "1469", "line": 2, "column": 10, "nodeType": "1245", "messageId": "1246", "endLine": 2, "endColumn": 19}, {"ruleId": "1302", "severity": 1, "message": "1331", "line": 29, "column": 45, "nodeType": "1304", "messageId": "1305", "endLine": 29, "endColumn": 47}, {"ruleId": "1470", "severity": 1, "message": "1471", "line": 7, "column": 24, "nodeType": "1472", "messageId": "1473", "endLine": 7, "endColumn": 25, "suggestions": "1474"}, {"ruleId": "1470", "severity": 1, "message": "1471", "line": 7, "column": 40, "nodeType": "1472", "messageId": "1473", "endLine": 7, "endColumn": 41, "suggestions": "1475"}, {"ruleId": "1243", "severity": 1, "message": "1476", "line": 6, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 6, "endColumn": 15}, {"ruleId": "1243", "severity": 1, "message": "1477", "line": 9, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 9, "endColumn": 18}, {"ruleId": "1243", "severity": 1, "message": "1294", "line": 10, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 10, "endColumn": 15}, {"ruleId": "1243", "severity": 1, "message": "1478", "line": 12, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 12, "endColumn": 16}, {"ruleId": "1243", "severity": 1, "message": "1248", "line": 14, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 14, "endColumn": 14}, {"ruleId": "1243", "severity": 1, "message": "1479", "line": 15, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 15, "endColumn": 12}, {"ruleId": "1243", "severity": 1, "message": "1480", "line": 16, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 16, "endColumn": 16}, {"ruleId": "1243", "severity": 1, "message": "1481", "line": 22, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 22, "endColumn": 26}, {"ruleId": "1243", "severity": 1, "message": "1482", "line": 28, "column": 10, "nodeType": "1245", "messageId": "1246", "endLine": 28, "endColumn": 18}, {"ruleId": "1243", "severity": 1, "message": "1483", "line": 31, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 31, "endColumn": 28}, {"ruleId": "1243", "severity": 1, "message": "1484", "line": 32, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 32, "endColumn": 34}, {"ruleId": "1243", "severity": 1, "message": "1485", "line": 33, "column": 10, "nodeType": "1245", "messageId": "1246", "endLine": 33, "endColumn": 22}, {"ruleId": "1243", "severity": 1, "message": "1486", "line": 41, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 41, "endColumn": 26}, {"ruleId": "1243", "severity": 1, "message": "1487", "line": 92, "column": 7, "nodeType": "1245", "messageId": "1246", "endLine": 92, "endColumn": 13}, {"ruleId": "1243", "severity": 1, "message": "1488", "line": 156, "column": 9, "nodeType": "1245", "messageId": "1246", "endLine": 156, "endColumn": 14}, {"ruleId": "1255", "severity": 1, "message": "1489", "line": 169, "column": 6, "nodeType": "1257", "endLine": 169, "endColumn": 8, "suggestions": "1490"}, {"ruleId": "1243", "severity": 1, "message": "1491", "line": 171, "column": 9, "nodeType": "1245", "messageId": "1246", "endLine": 171, "endColumn": 28}, {"ruleId": "1243", "severity": 1, "message": "1492", "line": 340, "column": 9, "nodeType": "1245", "messageId": "1246", "endLine": 340, "endColumn": 39}, {"ruleId": "1470", "severity": 1, "message": "1471", "line": 9, "column": 24, "nodeType": "1472", "messageId": "1473", "endLine": 9, "endColumn": 25, "suggestions": "1493"}, {"ruleId": "1470", "severity": 1, "message": "1471", "line": 9, "column": 40, "nodeType": "1472", "messageId": "1473", "endLine": 9, "endColumn": 41, "suggestions": "1494"}, {"ruleId": "1243", "severity": 1, "message": "1495", "line": 1, "column": 17, "nodeType": "1245", "messageId": "1246", "endLine": 1, "endColumn": 26}, {"ruleId": "1243", "severity": 1, "message": "1496", "line": 1, "column": 28, "nodeType": "1245", "messageId": "1246", "endLine": 1, "endColumn": 35}, {"ruleId": "1243", "severity": 1, "message": "1294", "line": 4, "column": 10, "nodeType": "1245", "messageId": "1246", "endLine": 4, "endColumn": 17}, {"ruleId": "1243", "severity": 1, "message": "1457", "line": 4, "column": 10, "nodeType": "1245", "messageId": "1246", "endLine": 4, "endColumn": 14}, {"ruleId": "1243", "severity": 1, "message": "1458", "line": 4, "column": 16, "nodeType": "1245", "messageId": "1246", "endLine": 4, "endColumn": 27}, {"ruleId": "1243", "severity": 1, "message": "1497", "line": 8, "column": 9, "nodeType": "1245", "messageId": "1246", "endLine": 8, "endColumn": 15}, {"ruleId": "1243", "severity": 1, "message": "1498", "line": 2, "column": 16, "nodeType": "1245", "messageId": "1246", "endLine": 2, "endColumn": 25}, {"ruleId": "1243", "severity": 1, "message": "1499", "line": 2, "column": 10, "nodeType": "1245", "messageId": "1246", "endLine": 2, "endColumn": 13}, {"ruleId": "1243", "severity": 1, "message": "1498", "line": 2, "column": 15, "nodeType": "1245", "messageId": "1246", "endLine": 2, "endColumn": 24}, {"ruleId": "1243", "severity": 1, "message": "1457", "line": 3, "column": 3, "nodeType": "1245", "messageId": "1246", "endLine": 3, "endColumn": 7}, {"ruleId": "1243", "severity": 1, "message": "1458", "line": 4, "column": 3, "nodeType": "1245", "messageId": "1246", "endLine": 4, "endColumn": 14}, {"ruleId": "1243", "severity": 1, "message": "1469", "line": 1, "column": 10, "nodeType": "1245", "messageId": "1246", "endLine": 1, "endColumn": 19}, {"ruleId": "1243", "severity": 1, "message": "1469", "line": 1, "column": 10, "nodeType": "1245", "messageId": "1246", "endLine": 1, "endColumn": 19}, {"ruleId": "1243", "severity": 1, "message": "1469", "line": 1, "column": 10, "nodeType": "1245", "messageId": "1246", "endLine": 1, "endColumn": 19}, {"ruleId": "1243", "severity": 1, "message": "1500", "line": 11, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 11, "endColumn": 21}, {"ruleId": "1243", "severity": 1, "message": "1288", "line": 32, "column": 27, "nodeType": "1245", "messageId": "1246", "endLine": 32, "endColumn": 34}, {"ruleId": "1243", "severity": 1, "message": "1329", "line": 42, "column": 11, "nodeType": "1245", "messageId": "1246", "endLine": 42, "endColumn": 19}, {"ruleId": "1243", "severity": 1, "message": "1432", "line": 1, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 1, "endColumn": 14}, {"ruleId": "1243", "severity": 1, "message": "1383", "line": 2, "column": 23, "nodeType": "1245", "messageId": "1246", "endLine": 2, "endColumn": 27}, {"ruleId": "1243", "severity": 1, "message": "1347", "line": 2, "column": 29, "nodeType": "1245", "messageId": "1246", "endLine": 2, "endColumn": 34}, {"ruleId": "1243", "severity": 1, "message": "1384", "line": 2, "column": 36, "nodeType": "1245", "messageId": "1246", "endLine": 2, "endColumn": 41}, {"ruleId": "1255", "severity": 1, "message": "1501", "line": 63, "column": 6, "nodeType": "1257", "endLine": 63, "endColumn": 8, "suggestions": "1502"}, {"ruleId": "1243", "severity": 1, "message": "1372", "line": 4, "column": 3, "nodeType": "1245", "messageId": "1246", "endLine": 4, "endColumn": 8}, {"ruleId": "1243", "severity": 1, "message": "1503", "line": 8, "column": 3, "nodeType": "1245", "messageId": "1246", "endLine": 8, "endColumn": 15}, {"ruleId": "1243", "severity": 1, "message": "1358", "line": 25, "column": 3, "nodeType": "1245", "messageId": "1246", "endLine": 25, "endColumn": 9}, {"ruleId": "1243", "severity": 1, "message": "1504", "line": 26, "column": 3, "nodeType": "1245", "messageId": "1246", "endLine": 26, "endColumn": 9}, {"ruleId": "1243", "severity": 1, "message": "1357", "line": 27, "column": 3, "nodeType": "1245", "messageId": "1246", "endLine": 27, "endColumn": 13}, {"ruleId": "1243", "severity": 1, "message": "1476", "line": 28, "column": 3, "nodeType": "1245", "messageId": "1246", "endLine": 28, "endColumn": 10}, {"ruleId": "1243", "severity": 1, "message": "1505", "line": 48, "column": 10, "nodeType": "1245", "messageId": "1246", "endLine": 48, "endColumn": 18}, {"ruleId": "1243", "severity": 1, "message": "1506", "line": 50, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 50, "endColumn": 17}, {"ruleId": "1243", "severity": 1, "message": "1457", "line": 52, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 52, "endColumn": 12}, {"ruleId": "1243", "severity": 1, "message": "1458", "line": 53, "column": 10, "nodeType": "1245", "messageId": "1246", "endLine": 53, "endColumn": 21}, {"ruleId": "1243", "severity": 1, "message": "1418", "line": 54, "column": 10, "nodeType": "1245", "messageId": "1246", "endLine": 54, "endColumn": 19}, {"ruleId": "1243", "severity": 1, "message": "1383", "line": 54, "column": 21, "nodeType": "1245", "messageId": "1246", "endLine": 54, "endColumn": 25}, {"ruleId": "1243", "severity": 1, "message": "1507", "line": 55, "column": 10, "nodeType": "1245", "messageId": "1246", "endLine": 55, "endColumn": 22}, {"ruleId": "1243", "severity": 1, "message": "1508", "line": 55, "column": 24, "nodeType": "1245", "messageId": "1246", "endLine": 55, "endColumn": 39}, {"ruleId": "1243", "severity": 1, "message": "1509", "line": 56, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 56, "endColumn": 31}, {"ruleId": "1243", "severity": 1, "message": "1439", "line": 57, "column": 26, "nodeType": "1245", "messageId": "1246", "endLine": 57, "endColumn": 40}, {"ruleId": "1243", "severity": 1, "message": "1510", "line": 107, "column": 10, "nodeType": "1245", "messageId": "1246", "endLine": 107, "endColumn": 14}, {"ruleId": "1243", "severity": 1, "message": "1511", "line": 108, "column": 10, "nodeType": "1245", "messageId": "1246", "endLine": 108, "endColumn": 23}, {"ruleId": "1243", "severity": 1, "message": "1512", "line": 109, "column": 10, "nodeType": "1245", "messageId": "1246", "endLine": 109, "endColumn": 25}, {"ruleId": "1243", "severity": 1, "message": "1513", "line": 111, "column": 9, "nodeType": "1245", "messageId": "1246", "endLine": 111, "endColumn": 19}, {"ruleId": "1243", "severity": 1, "message": "1514", "line": 112, "column": 9, "nodeType": "1245", "messageId": "1246", "endLine": 112, "endColumn": 20}, {"ruleId": "1243", "severity": 1, "message": "1515", "line": 117, "column": 9, "nodeType": "1245", "messageId": "1246", "endLine": 117, "endColumn": 29}, {"ruleId": "1243", "severity": 1, "message": "1516", "line": 125, "column": 9, "nodeType": "1245", "messageId": "1246", "endLine": 125, "endColumn": 32}, {"ruleId": "1243", "severity": 1, "message": "1517", "line": 131, "column": 10, "nodeType": "1245", "messageId": "1246", "endLine": 131, "endColumn": 22}, {"ruleId": "1243", "severity": 1, "message": "1518", "line": 133, "column": 9, "nodeType": "1245", "messageId": "1246", "endLine": 133, "endColumn": 19}, {"ruleId": "1243", "severity": 1, "message": "1519", "line": 142, "column": 9, "nodeType": "1245", "messageId": "1246", "endLine": 142, "endColumn": 19}, {"ruleId": "1243", "severity": 1, "message": "1301", "line": 177, "column": 25, "nodeType": "1245", "messageId": "1246", "endLine": 177, "endColumn": 41}, {"ruleId": "1255", "severity": 1, "message": "1520", "line": 222, "column": 6, "nodeType": "1257", "endLine": 222, "endColumn": 8, "suggestions": "1521"}, {"ruleId": "1255", "severity": 1, "message": "1522", "line": 244, "column": 6, "nodeType": "1257", "endLine": 244, "endColumn": 68, "suggestions": "1523"}, {"ruleId": "1302", "severity": 1, "message": "1303", "line": 426, "column": 64, "nodeType": "1304", "messageId": "1305", "endLine": 426, "endColumn": 66}, {"ruleId": "1302", "severity": 1, "message": "1303", "line": 432, "column": 66, "nodeType": "1304", "messageId": "1305", "endLine": 432, "endColumn": 68}, {"ruleId": "1302", "severity": 1, "message": "1331", "line": 450, "column": 50, "nodeType": "1304", "messageId": "1305", "endLine": 450, "endColumn": 52}, {"ruleId": "1302", "severity": 1, "message": "1303", "line": 743, "column": 61, "nodeType": "1304", "messageId": "1305", "endLine": 743, "endColumn": 63}, {"ruleId": "1243", "severity": 1, "message": "1244", "line": 1, "column": 46, "nodeType": "1245", "messageId": "1246", "endLine": 1, "endColumn": 56}, {"ruleId": "1243", "severity": 1, "message": "1503", "line": 7, "column": 3, "nodeType": "1245", "messageId": "1246", "endLine": 7, "endColumn": 15}, {"ruleId": "1243", "severity": 1, "message": "1524", "line": 19, "column": 10, "nodeType": "1245", "messageId": "1246", "endLine": 19, "endColumn": 24}, {"ruleId": "1255", "severity": 1, "message": "1525", "line": 58, "column": 6, "nodeType": "1257", "endLine": 58, "endColumn": 39, "suggestions": "1526"}, {"ruleId": "1255", "severity": 1, "message": "1527", "line": 137, "column": 6, "nodeType": "1257", "endLine": 137, "endColumn": 36, "suggestions": "1528"}, {"ruleId": "1243", "severity": 1, "message": "1469", "line": 1, "column": 10, "nodeType": "1245", "messageId": "1246", "endLine": 1, "endColumn": 19}, {"ruleId": "1243", "severity": 1, "message": "1529", "line": 6, "column": 3, "nodeType": "1245", "messageId": "1246", "endLine": 6, "endColumn": 17}, {"ruleId": "1243", "severity": 1, "message": "1530", "line": 11, "column": 10, "nodeType": "1245", "messageId": "1246", "endLine": 11, "endColumn": 27}, {"ruleId": "1243", "severity": 1, "message": "1531", "line": 4, "column": 3, "nodeType": "1245", "messageId": "1246", "endLine": 4, "endColumn": 16}, {"ruleId": "1243", "severity": 1, "message": "1248", "line": 5, "column": 3, "nodeType": "1245", "messageId": "1246", "endLine": 5, "endColumn": 9}, {"ruleId": "1243", "severity": 1, "message": "1477", "line": 6, "column": 3, "nodeType": "1245", "messageId": "1246", "endLine": 6, "endColumn": 13}, {"ruleId": "1243", "severity": 1, "message": "1532", "line": 7, "column": 3, "nodeType": "1245", "messageId": "1246", "endLine": 7, "endColumn": 6}, {"ruleId": "1243", "severity": 1, "message": "1357", "line": 8, "column": 3, "nodeType": "1245", "messageId": "1246", "endLine": 8, "endColumn": 13}, {"ruleId": "1243", "severity": 1, "message": "1504", "line": 9, "column": 3, "nodeType": "1245", "messageId": "1246", "endLine": 9, "endColumn": 9}, {"ruleId": "1243", "severity": 1, "message": "1476", "line": 10, "column": 3, "nodeType": "1245", "messageId": "1246", "endLine": 10, "endColumn": 10}, {"ruleId": "1243", "severity": 1, "message": "1402", "line": 11, "column": 3, "nodeType": "1245", "messageId": "1246", "endLine": 11, "endColumn": 7}, {"ruleId": "1243", "severity": 1, "message": "1403", "line": 12, "column": 3, "nodeType": "1245", "messageId": "1246", "endLine": 12, "endColumn": 11}, {"ruleId": "1243", "severity": 1, "message": "1503", "line": 13, "column": 3, "nodeType": "1245", "messageId": "1246", "endLine": 13, "endColumn": 15}, {"ruleId": "1243", "severity": 1, "message": "1294", "line": 14, "column": 3, "nodeType": "1245", "messageId": "1246", "endLine": 14, "endColumn": 10}, {"ruleId": "1243", "severity": 1, "message": "1533", "line": 17, "column": 3, "nodeType": "1245", "messageId": "1246", "endLine": 17, "endColumn": 17}, {"ruleId": "1243", "severity": 1, "message": "1506", "line": 19, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 19, "endColumn": 17}, {"ruleId": "1243", "severity": 1, "message": "1534", "line": 20, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 20, "endColumn": 21}, {"ruleId": "1243", "severity": 1, "message": "1535", "line": 21, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 21, "endColumn": 24}, {"ruleId": "1243", "severity": 1, "message": "1536", "line": 22, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 22, "endColumn": 20}, {"ruleId": "1243", "severity": 1, "message": "1537", "line": 23, "column": 10, "nodeType": "1245", "messageId": "1246", "endLine": 23, "endColumn": 16}, {"ruleId": "1243", "severity": 1, "message": "1538", "line": 23, "column": 18, "nodeType": "1245", "messageId": "1246", "endLine": 23, "endColumn": 22}, {"ruleId": "1243", "severity": 1, "message": "1282", "line": 24, "column": 13, "nodeType": "1245", "messageId": "1246", "endLine": 24, "endColumn": 16}, {"ruleId": "1243", "severity": 1, "message": "1539", "line": 33, "column": 11, "nodeType": "1245", "messageId": "1246", "endLine": 33, "endColumn": 19}, {"ruleId": "1243", "severity": 1, "message": "1540", "line": 4, "column": 3, "nodeType": "1245", "messageId": "1246", "endLine": 4, "endColumn": 14}, {"ruleId": "1243", "severity": 1, "message": "1249", "line": 10, "column": 3, "nodeType": "1245", "messageId": "1246", "endLine": 10, "endColumn": 12}, {"ruleId": "1243", "severity": 1, "message": "1460", "line": 14, "column": 3, "nodeType": "1245", "messageId": "1246", "endLine": 14, "endColumn": 8}, {"ruleId": "1243", "severity": 1, "message": "1541", "line": 21, "column": 10, "nodeType": "1245", "messageId": "1246", "endLine": 21, "endColumn": 22}, {"ruleId": "1243", "severity": 1, "message": "1542", "line": 22, "column": 10, "nodeType": "1245", "messageId": "1246", "endLine": 22, "endColumn": 30}, {"ruleId": "1243", "severity": 1, "message": "1543", "line": 22, "column": 32, "nodeType": "1245", "messageId": "1246", "endLine": 22, "endColumn": 42}, {"ruleId": "1243", "severity": 1, "message": "1383", "line": 15, "column": 3, "nodeType": "1245", "messageId": "1246", "endLine": 15, "endColumn": 7}, {"ruleId": "1243", "severity": 1, "message": "1294", "line": 15, "column": 3, "nodeType": "1245", "messageId": "1246", "endLine": 15, "endColumn": 10}, {"ruleId": "1243", "severity": 1, "message": "1495", "line": 1, "column": 27, "nodeType": "1245", "messageId": "1246", "endLine": 1, "endColumn": 36}, {"ruleId": "1243", "severity": 1, "message": "1544", "line": 13, "column": 13, "nodeType": "1245", "messageId": "1246", "endLine": 13, "endColumn": 23}, {"ruleId": "1243", "severity": 1, "message": "1469", "line": 1, "column": 10, "nodeType": "1245", "messageId": "1246", "endLine": 1, "endColumn": 19}, {"ruleId": "1243", "severity": 1, "message": "1545", "line": 4, "column": 3, "nodeType": "1245", "messageId": "1246", "endLine": 4, "endColumn": 20}, {"ruleId": "1243", "severity": 1, "message": "1469", "line": 1, "column": 10, "nodeType": "1245", "messageId": "1246", "endLine": 1, "endColumn": 19}, {"ruleId": "1243", "severity": 1, "message": "1545", "line": 10, "column": 3, "nodeType": "1245", "messageId": "1246", "endLine": 10, "endColumn": 20}, {"ruleId": "1243", "severity": 1, "message": "1546", "line": 11, "column": 3, "nodeType": "1245", "messageId": "1246", "endLine": 11, "endColumn": 15}, {"ruleId": "1243", "severity": 1, "message": "1547", "line": 1, "column": 17, "nodeType": "1245", "messageId": "1246", "endLine": 1, "endColumn": 25}, {"ruleId": "1243", "severity": 1, "message": "1548", "line": 10, "column": 10, "nodeType": "1245", "messageId": "1246", "endLine": 10, "endColumn": 21}, {"ruleId": "1243", "severity": 1, "message": "1549", "line": 8, "column": 10, "nodeType": "1245", "messageId": "1246", "endLine": 8, "endColumn": 28}, {"ruleId": "1243", "severity": 1, "message": "1383", "line": 29, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 29, "endColumn": 12}, {"ruleId": "1243", "severity": 1, "message": "1550", "line": 52, "column": 10, "nodeType": "1245", "messageId": "1246", "endLine": 52, "endColumn": 15}, {"ruleId": "1243", "severity": 1, "message": "1551", "line": 57, "column": 10, "nodeType": "1245", "messageId": "1246", "endLine": 57, "endColumn": 28}, {"ruleId": "1243", "severity": 1, "message": "1552", "line": 58, "column": 10, "nodeType": "1245", "messageId": "1246", "endLine": 58, "endColumn": 16}, {"ruleId": "1243", "severity": 1, "message": "1553", "line": 59, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 59, "endColumn": 19}, {"ruleId": "1243", "severity": 1, "message": "1554", "line": 73, "column": 10, "nodeType": "1245", "messageId": "1246", "endLine": 73, "endColumn": 16}, {"ruleId": "1243", "severity": 1, "message": "1555", "line": 73, "column": 18, "nodeType": "1245", "messageId": "1246", "endLine": 73, "endColumn": 27}, {"ruleId": "1243", "severity": 1, "message": "1288", "line": 76, "column": 27, "nodeType": "1245", "messageId": "1246", "endLine": 76, "endColumn": 34}, {"ruleId": "1302", "severity": 1, "message": "1331", "line": 143, "column": 53, "nodeType": "1304", "messageId": "1305", "endLine": 143, "endColumn": 55}, {"ruleId": "1255", "severity": 1, "message": "1556", "line": 152, "column": 6, "nodeType": "1257", "endLine": 152, "endColumn": 8, "suggestions": "1557"}, {"ruleId": "1255", "severity": 1, "message": "1378", "line": 373, "column": 6, "nodeType": "1257", "endLine": 373, "endColumn": 8, "suggestions": "1558"}, {"ruleId": "1243", "severity": 1, "message": "1469", "line": 1, "column": 10, "nodeType": "1245", "messageId": "1246", "endLine": 1, "endColumn": 19}, {"ruleId": "1243", "severity": 1, "message": "1559", "line": 6, "column": 3, "nodeType": "1245", "messageId": "1246", "endLine": 6, "endColumn": 19}, {"ruleId": "1243", "severity": 1, "message": "1560", "line": 29, "column": 9, "nodeType": "1245", "messageId": "1246", "endLine": 29, "endColumn": 17}, {"ruleId": "1243", "severity": 1, "message": "1432", "line": 1, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 1, "endColumn": 14}, {"ruleId": "1243", "severity": 1, "message": "1495", "line": 2, "column": 10, "nodeType": "1245", "messageId": "1246", "endLine": 2, "endColumn": 19}, {"ruleId": "1243", "severity": 1, "message": "1547", "line": 2, "column": 21, "nodeType": "1245", "messageId": "1246", "endLine": 2, "endColumn": 29}, {"ruleId": "1243", "severity": 1, "message": "1458", "line": 5, "column": 3, "nodeType": "1245", "messageId": "1246", "endLine": 5, "endColumn": 14}, {"ruleId": "1243", "severity": 1, "message": "1432", "line": 8, "column": 3, "nodeType": "1245", "messageId": "1246", "endLine": 8, "endColumn": 9}, {"ruleId": "1243", "severity": 1, "message": "1561", "line": 10, "column": 3, "nodeType": "1245", "messageId": "1246", "endLine": 10, "endColumn": 8}, {"ruleId": "1243", "severity": 1, "message": "1402", "line": 9, "column": 3, "nodeType": "1245", "messageId": "1246", "endLine": 9, "endColumn": 7}, {"ruleId": "1243", "severity": 1, "message": "1403", "line": 10, "column": 3, "nodeType": "1245", "messageId": "1246", "endLine": 10, "endColumn": 11}, {"ruleId": "1243", "severity": 1, "message": "1503", "line": 11, "column": 3, "nodeType": "1245", "messageId": "1246", "endLine": 11, "endColumn": 15}, {"ruleId": "1243", "severity": 1, "message": "1294", "line": 12, "column": 3, "nodeType": "1245", "messageId": "1246", "endLine": 12, "endColumn": 10}, {"ruleId": "1243", "severity": 1, "message": "1562", "line": 12, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 12, "endColumn": 14}, {"ruleId": "1243", "severity": 1, "message": "1563", "line": 12, "column": 18, "nodeType": "1245", "messageId": "1246", "endLine": 12, "endColumn": 35}, {"ruleId": "1243", "severity": 1, "message": "1480", "line": 13, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 13, "endColumn": 16}, {"ruleId": "1243", "severity": 1, "message": "1564", "line": 14, "column": 10, "nodeType": "1245", "messageId": "1246", "endLine": 14, "endColumn": 15}, {"ruleId": "1243", "severity": 1, "message": "1286", "line": 16, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 16, "endColumn": 18}, {"ruleId": "1243", "severity": 1, "message": "1284", "line": 17, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 17, "endColumn": 19}, {"ruleId": "1243", "severity": 1, "message": "1565", "line": 20, "column": 10, "nodeType": "1245", "messageId": "1246", "endLine": 20, "endColumn": 24}, {"ruleId": "1243", "severity": 1, "message": "1566", "line": 21, "column": 10, "nodeType": "1245", "messageId": "1246", "endLine": 21, "endColumn": 19}, {"ruleId": "1243", "severity": 1, "message": "1567", "line": 45, "column": 10, "nodeType": "1245", "messageId": "1246", "endLine": 45, "endColumn": 19}, {"ruleId": "1243", "severity": 1, "message": "1301", "line": 59, "column": 25, "nodeType": "1245", "messageId": "1246", "endLine": 59, "endColumn": 41}, {"ruleId": "1255", "severity": 1, "message": "1568", "line": 81, "column": 6, "nodeType": "1257", "endLine": 81, "endColumn": 8, "suggestions": "1569"}, {"ruleId": "1243", "severity": 1, "message": "1402", "line": 9, "column": 3, "nodeType": "1245", "messageId": "1246", "endLine": 9, "endColumn": 7}, {"ruleId": "1243", "severity": 1, "message": "1403", "line": 10, "column": 3, "nodeType": "1245", "messageId": "1246", "endLine": 10, "endColumn": 11}, {"ruleId": "1243", "severity": 1, "message": "1503", "line": 11, "column": 3, "nodeType": "1245", "messageId": "1246", "endLine": 11, "endColumn": 15}, {"ruleId": "1243", "severity": 1, "message": "1294", "line": 12, "column": 3, "nodeType": "1245", "messageId": "1246", "endLine": 12, "endColumn": 10}, {"ruleId": "1570", "severity": 1, "message": "1571", "line": 37, "column": 61, "nodeType": "1572", "messageId": "1573", "endLine": 37, "endColumn": 63}, {"ruleId": "1243", "severity": 1, "message": "1477", "line": 5, "column": 3, "nodeType": "1245", "messageId": "1246", "endLine": 5, "endColumn": 13}, {"ruleId": "1243", "severity": 1, "message": "1432", "line": 13, "column": 16, "nodeType": "1245", "messageId": "1246", "endLine": 13, "endColumn": 22}, {"ruleId": "1243", "severity": 1, "message": "1574", "line": 13, "column": 24, "nodeType": "1245", "messageId": "1246", "endLine": 13, "endColumn": 40}, {"ruleId": "1243", "severity": 1, "message": "1561", "line": 13, "column": 42, "nodeType": "1245", "messageId": "1246", "endLine": 13, "endColumn": 47}, {"ruleId": "1243", "severity": 1, "message": "1398", "line": 14, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 14, "endColumn": 23}, {"ruleId": "1243", "severity": 1, "message": "1575", "line": 15, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 15, "endColumn": 20}, {"ruleId": "1243", "severity": 1, "message": "1576", "line": 17, "column": 10, "nodeType": "1245", "messageId": "1246", "endLine": 17, "endColumn": 29}, {"ruleId": "1243", "severity": 1, "message": "1577", "line": 18, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 18, "endColumn": 26}, {"ruleId": "1243", "severity": 1, "message": "1578", "line": 19, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 19, "endColumn": 29}, {"ruleId": "1243", "severity": 1, "message": "1579", "line": 20, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 20, "endColumn": 17}, {"ruleId": "1243", "severity": 1, "message": "1580", "line": 21, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 21, "endColumn": 25}, {"ruleId": "1243", "severity": 1, "message": "1248", "line": 8, "column": 3, "nodeType": "1245", "messageId": "1246", "endLine": 8, "endColumn": 9}, {"ruleId": "1243", "severity": 1, "message": "1581", "line": 10, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 10, "endColumn": 26}, {"ruleId": "1243", "severity": 1, "message": "1582", "line": 22, "column": 9, "nodeType": "1245", "messageId": "1246", "endLine": 22, "endColumn": 22}, {"ruleId": "1243", "severity": 1, "message": "1432", "line": 1, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 1, "endColumn": 14}, {"ruleId": "1243", "severity": 1, "message": "1495", "line": 2, "column": 10, "nodeType": "1245", "messageId": "1246", "endLine": 2, "endColumn": 19}, {"ruleId": "1243", "severity": 1, "message": "1547", "line": 2, "column": 21, "nodeType": "1245", "messageId": "1246", "endLine": 2, "endColumn": 29}, {"ruleId": "1243", "severity": 1, "message": "1583", "line": 2, "column": 3, "nodeType": "1245", "messageId": "1246", "endLine": 2, "endColumn": 20}, {"ruleId": "1243", "severity": 1, "message": "1495", "line": 4, "column": 3, "nodeType": "1245", "messageId": "1246", "endLine": 4, "endColumn": 12}, {"ruleId": "1243", "severity": 1, "message": "1288", "line": 38, "column": 27, "nodeType": "1245", "messageId": "1246", "endLine": 38, "endColumn": 34}, {"ruleId": "1243", "severity": 1, "message": "1329", "line": 49, "column": 11, "nodeType": "1245", "messageId": "1246", "endLine": 49, "endColumn": 19}, {"ruleId": "1243", "severity": 1, "message": "1562", "line": 7, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 7, "endColumn": 14}, {"ruleId": "1243", "severity": 1, "message": "1480", "line": 8, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 8, "endColumn": 16}, {"ruleId": "1243", "severity": 1, "message": "1286", "line": 9, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 9, "endColumn": 18}, {"ruleId": "1243", "severity": 1, "message": "1284", "line": 10, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 10, "endColumn": 19}, {"ruleId": "1243", "severity": 1, "message": "1584", "line": 12, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 12, "endColumn": 29}, {"ruleId": "1243", "severity": 1, "message": "1433", "line": 13, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 13, "endColumn": 23}, {"ruleId": "1243", "severity": 1, "message": "1585", "line": 19, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 19, "endColumn": 24}, {"ruleId": "1243", "severity": 1, "message": "1586", "line": 20, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 20, "endColumn": 20}, {"ruleId": "1243", "severity": 1, "message": "1587", "line": 21, "column": 10, "nodeType": "1245", "messageId": "1246", "endLine": 21, "endColumn": 21}, {"ruleId": "1243", "severity": 1, "message": "1367", "line": 21, "column": 23, "nodeType": "1245", "messageId": "1246", "endLine": 21, "endColumn": 32}, {"ruleId": "1243", "severity": 1, "message": "1294", "line": 21, "column": 41, "nodeType": "1245", "messageId": "1246", "endLine": 21, "endColumn": 48}, {"ruleId": "1243", "severity": 1, "message": "1588", "line": 22, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 22, "endColumn": 27}, {"ruleId": "1255", "severity": 1, "message": "1589", "line": 56, "column": 6, "nodeType": "1257", "endLine": 56, "endColumn": 8, "suggestions": "1590"}, {"ruleId": "1243", "severity": 1, "message": "1288", "line": 32, "column": 27, "nodeType": "1245", "messageId": "1246", "endLine": 32, "endColumn": 34}, {"ruleId": "1255", "severity": 1, "message": "1591", "line": 57, "column": 6, "nodeType": "1257", "endLine": 57, "endColumn": 8, "suggestions": "1592"}, {"ruleId": "1243", "severity": 1, "message": "1593", "line": 64, "column": 15, "nodeType": "1245", "messageId": "1246", "endLine": 64, "endColumn": 57}, {"ruleId": "1302", "severity": 1, "message": "1331", "line": 85, "column": 38, "nodeType": "1304", "messageId": "1305", "endLine": 85, "endColumn": 40}, {"ruleId": "1302", "severity": 1, "message": "1331", "line": 94, "column": 67, "nodeType": "1304", "messageId": "1305", "endLine": 94, "endColumn": 69}, {"ruleId": "1243", "severity": 1, "message": "1594", "line": 113, "column": 13, "nodeType": "1245", "messageId": "1246", "endLine": 113, "endColumn": 72}, {"ruleId": "1243", "severity": 1, "message": "1432", "line": 1, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 1, "endColumn": 14}, {"ruleId": "1255", "severity": 1, "message": "1595", "line": 15, "column": 6, "nodeType": "1257", "endLine": 15, "endColumn": 8, "suggestions": "1596"}, {"ruleId": "1597", "severity": 1, "message": "1598", "line": 22, "column": 9, "nodeType": "1599", "messageId": "1600", "endLine": 38, "endColumn": 10}, {"ruleId": "1243", "severity": 1, "message": "1469", "line": 1, "column": 10, "nodeType": "1245", "messageId": "1246", "endLine": 1, "endColumn": 19}, {"ruleId": "1243", "severity": 1, "message": "1601", "line": 8, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 8, "endColumn": 16}, {"ruleId": "1243", "severity": 1, "message": "1536", "line": 10, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 10, "endColumn": 20}, {"ruleId": "1243", "severity": 1, "message": "1530", "line": 27, "column": 10, "nodeType": "1245", "messageId": "1246", "endLine": 27, "endColumn": 27}, {"ruleId": "1243", "severity": 1, "message": "1560", "line": 30, "column": 9, "nodeType": "1245", "messageId": "1246", "endLine": 30, "endColumn": 17}, {"ruleId": "1243", "severity": 1, "message": "1288", "line": 36, "column": 27, "nodeType": "1245", "messageId": "1246", "endLine": 36, "endColumn": 34}, {"ruleId": "1332", "severity": 1, "message": "1333", "line": 126, "column": 11, "nodeType": "1334", "endLine": 137, "endColumn": 13}, {"ruleId": "1243", "severity": 1, "message": "1602", "line": 33, "column": 5, "nodeType": "1245", "messageId": "1246", "endLine": 33, "endColumn": 19}, {"ruleId": "1243", "severity": 1, "message": "1603", "line": 69, "column": 11, "nodeType": "1245", "messageId": "1246", "endLine": 69, "endColumn": 19}, {"ruleId": "1243", "severity": 1, "message": "1604", "line": 2, "column": 10, "nodeType": "1245", "messageId": "1246", "endLine": 2, "endColumn": 24}, {"ruleId": "1243", "severity": 1, "message": "1532", "line": 9, "column": 10, "nodeType": "1245", "messageId": "1246", "endLine": 9, "endColumn": 13}, {"ruleId": "1243", "severity": 1, "message": "1605", "line": 26, "column": 30, "nodeType": "1245", "messageId": "1246", "endLine": 26, "endColumn": 44}, {"ruleId": "1243", "severity": 1, "message": "1357", "line": 3, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 3, "endColumn": 18}, {"ruleId": "1243", "severity": 1, "message": "1483", "line": 4, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 4, "endColumn": 28}, {"ruleId": "1243", "severity": 1, "message": "1248", "line": 5, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 5, "endColumn": 14}, {"ruleId": "1243", "severity": 1, "message": "1479", "line": 7, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 7, "endColumn": 12}, {"ruleId": "1243", "severity": 1, "message": "1480", "line": 8, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 8, "endColumn": 16}, {"ruleId": "1243", "severity": 1, "message": "1606", "line": 11, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 11, "endColumn": 18}, {"ruleId": "1243", "severity": 1, "message": "1607", "line": 16, "column": 9, "nodeType": "1245", "messageId": "1246", "endLine": 16, "endColumn": 19}, {"ruleId": "1243", "severity": 1, "message": "1608", "line": 18, "column": 9, "nodeType": "1245", "messageId": "1246", "endLine": 18, "endColumn": 20}, {"ruleId": "1243", "severity": 1, "message": "1448", "line": 19, "column": 9, "nodeType": "1245", "messageId": "1246", "endLine": 19, "endColumn": 20}, {"ruleId": "1243", "severity": 1, "message": "1514", "line": 22, "column": 9, "nodeType": "1245", "messageId": "1246", "endLine": 22, "endColumn": 20}, {"ruleId": "1243", "severity": 1, "message": "1407", "line": 11, "column": 23, "nodeType": "1245", "messageId": "1246", "endLine": 11, "endColumn": 34}, {"ruleId": "1255", "severity": 1, "message": "1609", "line": 43, "column": 6, "nodeType": "1257", "endLine": 43, "endColumn": 8, "suggestions": "1610"}, {"ruleId": "1255", "severity": 1, "message": "1611", "line": 37, "column": 6, "nodeType": "1257", "endLine": 37, "endColumn": 8, "suggestions": "1612"}, {"ruleId": "1243", "severity": 1, "message": "1613", "line": 22, "column": 10, "nodeType": "1245", "messageId": "1246", "endLine": 22, "endColumn": 18}, {"ruleId": "1243", "severity": 1, "message": "1614", "line": 22, "column": 20, "nodeType": "1245", "messageId": "1246", "endLine": 22, "endColumn": 31}, {"ruleId": "1255", "severity": 1, "message": "1615", "line": 41, "column": 6, "nodeType": "1257", "endLine": 41, "endColumn": 26, "suggestions": "1616"}, {"ruleId": "1243", "severity": 1, "message": "1432", "line": 1, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 1, "endColumn": 14}, {"ruleId": "1243", "severity": 1, "message": "1383", "line": 3, "column": 27, "nodeType": "1245", "messageId": "1246", "endLine": 3, "endColumn": 31}, {"ruleId": "1255", "severity": 1, "message": "1615", "line": 136, "column": 6, "nodeType": "1257", "endLine": 136, "endColumn": 26, "suggestions": "1617"}, {"ruleId": "1243", "severity": 1, "message": "1618", "line": 4, "column": 10, "nodeType": "1245", "messageId": "1246", "endLine": 4, "endColumn": 13}, {"ruleId": "1243", "severity": 1, "message": "1432", "line": 5, "column": 3, "nodeType": "1245", "messageId": "1246", "endLine": 5, "endColumn": 9}, {"ruleId": "1243", "severity": 1, "message": "1619", "line": 6, "column": 3, "nodeType": "1245", "messageId": "1246", "endLine": 6, "endColumn": 9}, {"ruleId": "1243", "severity": 1, "message": "1618", "line": 11, "column": 10, "nodeType": "1245", "messageId": "1246", "endLine": 11, "endColumn": 13}, {"ruleId": "1243", "severity": 1, "message": "1620", "line": 12, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 12, "endColumn": 16}, {"ruleId": "1243", "severity": 1, "message": "1432", "line": 2, "column": 27, "nodeType": "1245", "messageId": "1246", "endLine": 2, "endColumn": 33}, {"ruleId": "1243", "severity": 1, "message": "1618", "line": 4, "column": 10, "nodeType": "1245", "messageId": "1246", "endLine": 4, "endColumn": 13}, {"ruleId": "1243", "severity": 1, "message": "1620", "line": 5, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 5, "endColumn": 16}, {"ruleId": "1243", "severity": 1, "message": "1432", "line": 5, "column": 3, "nodeType": "1245", "messageId": "1246", "endLine": 5, "endColumn": 9}, {"ruleId": "1243", "severity": 1, "message": "1619", "line": 6, "column": 3, "nodeType": "1245", "messageId": "1246", "endLine": 6, "endColumn": 9}, {"ruleId": "1243", "severity": 1, "message": "1618", "line": 11, "column": 10, "nodeType": "1245", "messageId": "1246", "endLine": 11, "endColumn": 13}, {"ruleId": "1243", "severity": 1, "message": "1620", "line": 12, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 12, "endColumn": 16}, {"ruleId": "1243", "severity": 1, "message": "1432", "line": 5, "column": 3, "nodeType": "1245", "messageId": "1246", "endLine": 5, "endColumn": 9}, {"ruleId": "1243", "severity": 1, "message": "1619", "line": 6, "column": 3, "nodeType": "1245", "messageId": "1246", "endLine": 6, "endColumn": 9}, {"ruleId": "1243", "severity": 1, "message": "1618", "line": 11, "column": 10, "nodeType": "1245", "messageId": "1246", "endLine": 11, "endColumn": 13}, {"ruleId": "1243", "severity": 1, "message": "1620", "line": 12, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 12, "endColumn": 16}, {"ruleId": "1621", "severity": 1, "message": "1622", "line": 50, "column": 15, "nodeType": "1334", "endLine": 52, "endColumn": 17}, {"ruleId": "1243", "severity": 1, "message": "1432", "line": 5, "column": 3, "nodeType": "1245", "messageId": "1246", "endLine": 5, "endColumn": 9}, {"ruleId": "1243", "severity": 1, "message": "1619", "line": 6, "column": 3, "nodeType": "1245", "messageId": "1246", "endLine": 6, "endColumn": 9}, {"ruleId": "1243", "severity": 1, "message": "1618", "line": 11, "column": 10, "nodeType": "1245", "messageId": "1246", "endLine": 11, "endColumn": 13}, {"ruleId": "1243", "severity": 1, "message": "1620", "line": 12, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 12, "endColumn": 16}, {"ruleId": "1243", "severity": 1, "message": "1623", "line": 35, "column": 7, "nodeType": "1245", "messageId": "1246", "endLine": 35, "endColumn": 25}, {"ruleId": "1243", "severity": 1, "message": "1404", "line": 15, "column": 3, "nodeType": "1245", "messageId": "1246", "endLine": 15, "endColumn": 9}, {"ruleId": "1243", "severity": 1, "message": "1624", "line": 16, "column": 3, "nodeType": "1245", "messageId": "1246", "endLine": 16, "endColumn": 19}, {"ruleId": "1243", "severity": 1, "message": "1249", "line": 17, "column": 3, "nodeType": "1245", "messageId": "1246", "endLine": 17, "endColumn": 12}, {"ruleId": "1243", "severity": 1, "message": "1294", "line": 22, "column": 3, "nodeType": "1245", "messageId": "1246", "endLine": 22, "endColumn": 10}, {"ruleId": "1243", "severity": 1, "message": "1625", "line": 28, "column": 15, "nodeType": "1245", "messageId": "1246", "endLine": 28, "endColumn": 27}, {"ruleId": "1243", "severity": 1, "message": "1620", "line": 29, "column": 11, "nodeType": "1245", "messageId": "1246", "endLine": 29, "endColumn": 19}, {"ruleId": "1243", "severity": 1, "message": "1341", "line": 30, "column": 15, "nodeType": "1245", "messageId": "1246", "endLine": 30, "endColumn": 27}, {"ruleId": "1243", "severity": 1, "message": "1626", "line": 41, "column": 3, "nodeType": "1245", "messageId": "1246", "endLine": 41, "endColumn": 30}, {"ruleId": "1243", "severity": 1, "message": "1627", "line": 100, "column": 10, "nodeType": "1245", "messageId": "1246", "endLine": 100, "endColumn": 29}, {"ruleId": "1255", "severity": 1, "message": "1628", "line": 116, "column": 6, "nodeType": "1257", "endLine": 116, "endColumn": 16, "suggestions": "1629"}, {"ruleId": "1255", "severity": 1, "message": "1630", "line": 123, "column": 6, "nodeType": "1257", "endLine": 123, "endColumn": 34, "suggestions": "1631"}, {"ruleId": "1243", "severity": 1, "message": "1632", "line": 252, "column": 9, "nodeType": "1245", "messageId": "1246", "endLine": 252, "endColumn": 35}, {"ruleId": "1243", "severity": 1, "message": "1633", "line": 257, "column": 9, "nodeType": "1245", "messageId": "1246", "endLine": 257, "endColumn": 27}, {"ruleId": "1243", "severity": 1, "message": "1495", "line": 1, "column": 29, "nodeType": "1245", "messageId": "1246", "endLine": 1, "endColumn": 38}, {"ruleId": "1243", "severity": 1, "message": "1547", "line": 1, "column": 40, "nodeType": "1245", "messageId": "1246", "endLine": 1, "endColumn": 48}, {"ruleId": "1243", "severity": 1, "message": "1495", "line": 1, "column": 29, "nodeType": "1245", "messageId": "1246", "endLine": 1, "endColumn": 38}, {"ruleId": "1243", "severity": 1, "message": "1547", "line": 1, "column": 40, "nodeType": "1245", "messageId": "1246", "endLine": 1, "endColumn": 48}, {"ruleId": "1243", "severity": 1, "message": "1437", "line": 16, "column": 3, "nodeType": "1245", "messageId": "1246", "endLine": 16, "endColumn": 7}, {"ruleId": "1243", "severity": 1, "message": "1574", "line": 24, "column": 3, "nodeType": "1245", "messageId": "1246", "endLine": 24, "endColumn": 19}, {"ruleId": "1243", "severity": 1, "message": "1373", "line": 38, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 38, "endColumn": 18}, {"ruleId": "1243", "severity": 1, "message": "1634", "line": 39, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 39, "endColumn": 22}, {"ruleId": "1243", "severity": 1, "message": "1635", "line": 40, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 40, "endColumn": 17}, {"ruleId": "1243", "severity": 1, "message": "1636", "line": 41, "column": 8, "nodeType": "1245", "messageId": "1246", "endLine": 41, "endColumn": 24}, {"ruleId": "1255", "severity": 1, "message": "1628", "line": 117, "column": 6, "nodeType": "1257", "endLine": 117, "endColumn": 8, "suggestions": "1637"}, {"ruleId": "1255", "severity": 1, "message": "1638", "line": 124, "column": 6, "nodeType": "1257", "endLine": 124, "endColumn": 26, "suggestions": "1639"}, {"ruleId": "1243", "severity": 1, "message": "1640", "line": 52, "column": 9, "nodeType": "1245", "messageId": "1246", "endLine": 52, "endColumn": 17}, {"ruleId": "1243", "severity": 1, "message": "1532", "line": 9, "column": 3, "nodeType": "1245", "messageId": "1246", "endLine": 9, "endColumn": 6}, {"ruleId": "1243", "severity": 1, "message": "1603", "line": 18, "column": 11, "nodeType": "1245", "messageId": "1246", "endLine": 18, "endColumn": 19}, {"ruleId": "1255", "severity": 1, "message": "1641", "line": 52, "column": 6, "nodeType": "1257", "endLine": 52, "endColumn": 8, "suggestions": "1642"}, {"ruleId": "1243", "severity": 1, "message": "1643", "line": 2, "column": 18, "nodeType": "1245", "messageId": "1246", "endLine": 2, "endColumn": 25}, {"ruleId": "1243", "severity": 1, "message": "1505", "line": 7, "column": 10, "nodeType": "1245", "messageId": "1246", "endLine": 7, "endColumn": 18}, {"ruleId": "1243", "severity": 1, "message": "1294", "line": 18, "column": 3, "nodeType": "1245", "messageId": "1246", "endLine": 18, "endColumn": 10}, {"ruleId": "1243", "severity": 1, "message": "1644", "line": 22, "column": 3, "nodeType": "1245", "messageId": "1246", "endLine": 22, "endColumn": 11}, {"ruleId": "1243", "severity": 1, "message": "1645", "line": 28, "column": 19, "nodeType": "1245", "messageId": "1246", "endLine": 28, "endColumn": 28}, {"ruleId": "1255", "severity": 1, "message": "1628", "line": 94, "column": 6, "nodeType": "1257", "endLine": 94, "endColumn": 12, "suggestions": "1646"}, {"ruleId": "1255", "severity": 1, "message": "1638", "line": 104, "column": 6, "nodeType": "1257", "endLine": 104, "endColumn": 26, "suggestions": "1647"}, "@typescript-eslint/no-unused-vars", "'useContext' is defined but never used.", "Identifier", "unusedVar", "'logo' is defined but never used.", "'Button' is defined but never used.", "'TextField' is defined but never used.", "'ThreeCircles' is defined but never used.", "'PreferencesContext' is defined but never used.", "'theme' is defined but never used.", "'appToastConfig' is assigned a value but never used.", "'setAppToastConfig' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useCallback has an unnecessary dependency: 'loading'. Either exclude it or remove the dependency array.", "ArrayExpression", ["1648"], "React Hook useMemo has a missing dependency: 'setLoading'. Either include it or remove the dependency array.", ["1649"], "'setActiveMenuItem' is assigned a value but never used.", "React Hook useCallback has an unnecessary dependency: 'activeMenuItem'. Either exclude it or remove the dependency array.", ["1650"], "React Hook useEffect has missing dependencies: 'location.pathname', 'loginMessage', and 'navigate'. Either include them or remove the dependency array.", ["1651"], "React Hook useEffect has a missing dependency: 'dispatchSessionExpired'. Either include it or remove the dependency array.", ["1652"], "React Hook useCallback has an unnecessary dependency: 'open'. Either exclude it or remove the dependency array.", ["1653"], ["1654"], "React Hook useCallback has an unnecessary dependency: 'toastConfig'. Either exclude it or remove the dependency array.", ["1655"], "React Hook useMemo has missing dependencies: 'setOpen', 'setToastConfig', and 'setToastMessage'. Either include them or remove the dependency array.", ["1656"], "'PaletteOptions' is defined but never used.", "'lookupReducer' is defined but never used.", "'LOGIN' is defined but never used.", "'createRef' is defined but never used.", "'axios' is defined but never used.", "'AnyAction' is defined but never used.", "'_applicationHelperService' is assigned a value but never used.", "'yup' is defined but never used.", "'Link' is defined but never used.", "'FormControl' is defined but never used.", "'FilledInput' is defined but never used.", "'InputLabel' is defined but never used.", "'MessageConstants' is defined but never used.", "'setOpen' is assigned a value but never used.", "'handleMouseDownPassword' is assigned a value but never used.", "'handleMouseUpPassword' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'title'. Either include it or remove the dependency array.", ["1657"], "'RevenueChartDashboard' is defined but never used.", "'Divider' is defined but never used.", "'Drawer' is defined but never used.", "'openqanda' is assigned a value but never used.", "'setOpenqanda' is assigned a value but never used.", "'_locationService' is assigned a value but never used.", "'businessGroupsOnBusiness' is assigned a value but never used.", "'paginationModel' is assigned a value but never used.", "'setInitialValues' is assigned a value but never used.", "eqeqeq", "Expected '!==' and instead saw '!='.", "BinaryExpression", "unexpected", "React Hook useEffect has missing dependencies: 'fetchLocationsPaginated', 'getBusiness', and 'getBusinessGroups'. Either include them or remove the dependency array.", ["1658"], "'HomeChartCard' is defined but never used.", "'originalLocationData' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'fetchLocations', 'getBusiness', and 'getBusinessGroups'. Either include them or remove the dependency array.", ["1659"], ["1660"], "'PageProps' is defined but never used.", "'ILoginModel' is defined but never used.", "'authInitiate' is defined but never used.", "'CalendarToday' is defined but never used.", "'IFileUploadResponseModel' is defined but never used.", "'ScheduleLater' is defined but never used.", "'GenericDrawer' is defined but never used.", "'date' is assigned a value but never used.", "'setDate' is assigned a value but never used.", "'scheduleForLater' is assigned a value but never used.", "'setScheduleForLater' is assigned a value but never used.", "'selectedFromGallery' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'location.state' and 'title'. Either include them or remove the dependency array.", ["1661"], "'checkFormValidity' is assigned a value but never used.", "'isValid' is assigned a value but never used.", "'response' is assigned a value but never used.", "'formatDayJsToISO' is assigned a value but never used.", "Expected '===' and instead saw '=='.", "jsx-a11y/img-redundant-alt", "Redundant alt attribute. Screen-readers already announce `img` tags as an image. You don’t need to use the words `image`, `photo,` or `picture` (or any specified custom words) in the alt prop.", "JSXOpeningElement", "'RegisteredEmployeesChart' is defined but never used.", "'ActiveJobsChart' is defined but never used.", "'PieChart' is defined but never used.", "'GroupIcon' is defined but never used.", "'WorkHistoryIcon' is defined but never used.", "'EventAvailableIcon' is defined but never used.", "'ScheduleIcon' is defined but never used.", "'EditLocationAltIcon' is defined but never used.", "'HelpIcon' is defined but never used.", "'ArrowUpwardRoundedIcon' is defined but never used.", "'ArrowDownwardRoundedIcon' is defined but never used.", "'FormHelperText' is defined but never used.", "'Grid2' is defined but never used.", "'BusinessInteractionsChart' is defined but never used.", "'SearchQueriesList' is defined but never used.", "'rbAccess' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchLocations'. Either include it or remove the dependency array.", ["1662"], "React Hook useEffect has a missing dependency: 'locationList'. Either include it or remove the dependency array. You can also replace multiple useState variables with useReducer if 'setSelectedAccountId' needs the current value of 'locationList'.", ["1663"], "React Hook useEffect has missing dependencies: 'fetchAnalyticsData', 'locationList', and 'selectedAccountId'. Either include them or remove the dependency array.", ["1664"], "'IconButton' is defined but never used.", "'Dialog' is defined but never used.", "'AddIcon' is defined but never used.", "'ServicesDisplay' is defined but never used.", "'Accessibility' is defined but never used.", "'FlagIcon' is defined but never used.", "'ChatIcon' is defined but never used.", "'LanguageIcon' is defined but never used.", "'LocationOnIcon' is defined but never used.", "'useCallback' is defined but never used.", "'Container' is defined but never used.", "'activeTab' is assigned a value but never used.", "'setActiveTab' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadSavedConfigurations'. Either include it or remove the dependency array.", ["1665"], "'Modal' is defined but never used.", "'DeleteIcon' is defined but never used.", "'SendIcon' is defined but never used.", "'DeleteOutlineIcon' is defined but never used.", "React Hook useEffect has missing dependencies: 'navigate' and 'userInfo.roleId'. Either include them or remove the dependency array.", ["1666"], "React Hook useEffect has a missing dependency: 'getRolesList'. Either include it or remove the dependency array.", ["1667"], "'SearchOffIcon' is defined but never used.", "'TableRowsRoundedIcon' is defined but never used.", "'IUsersListResponse' is defined but never used.", "'Grid' is defined but never used.", "'Stack' is defined but never used.", "'TablePagination' is defined but never used.", "'searchText' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchUsersPaginated'. Either include it or remove the dependency array.", ["1668"], "'rowsPerPage' is assigned a value but never used.", "'handleChangePage' is assigned a value but never used.", "'handleChangeRowsPerPage' is assigned a value but never used.", "'BlockOutlinedIcon' is defined but never used.", "'CheckCircleRoundedIcon' is defined but never used.", "'IBusinessListResponseModel' is defined but never used.", "'FormGroup' is defined but never used.", "'VerifiedIcon' is defined but never used.", "'PauseCircleFilledIcon' is defined but never used.", "'CheckCircleIcon' is defined but never used.", "'setAlertConfig' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'fetchBusinessPaginated', 'title', and 'userInfo'. Either include them or remove the dependency array.", ["1669"], "'List' is defined but never used.", "'ListItem' is defined but never used.", "'Switch' is defined but never used.", "'businessPreview' is defined but never used.", "'LockOpenIcon' is defined but never used.", "'useSelector' is defined but never used.", "'CancelOutlinedIcon' is defined but never used.", "'CampaignRoundedIcon' is defined but never used.", "'label' is assigned a value but never used.", "'StatusCardProps' is defined but never used.", "'progress' is assigned a value but never used.", "'setProgress' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'accountId', 'businessId', 'getLocationSummary', and 'locationId'. Either include them or remove the dependency array.", ["1670"], "React Hook useEffect has a missing dependency: 'performMissingInformationOperation'. Either include it or remove the dependency array.", ["1671"], "'CardMedia' is defined but never used.", "'showConfirmPopup' is assigned a value but never used.", "'setShowConfirmPopup' is assigned a value but never used.", ["1672"], "React Hook useEffect has a missing dependency: 'fetchLocationsPaginated'. Either include it or remove the dependency array.", ["1673"], "React Hook useEffect has missing dependencies: 'getBusiness' and 'getBusinessGroups'. Either include them or remove the dependency array.", ["1674"], "'FallingLines' is defined but never used.", "'RotatingLines' is defined but never used.", "'IDeleteRecord' is defined but never used.", "React Hook useEffect has a missing dependency: 'gmbCallBack'. Either include it or remove the dependency array.", ["1675"], "'StarBorderIcon' is defined but never used.", "'Avatar' is defined but never used.", "'StarRoundedIcon' is defined but never used.", "'ILocationListRequestModel' is defined but never used.", "'STARRATINGMAP' is defined but never used.", "'UserAvatar' is defined but never used.", "'Chip' is defined but never used.", "'SearchOutlinedIcon' is defined but never used.", "'InputAdornment' is defined but never used.", "'newestIcon' is assigned a value but never used.", "'oldestIcon' is assigned a value but never used.", "'highRatingIcon' is assigned a value but never used.", "'lowRatingIcon' is assigned a value but never used.", "'showScroll' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'fetchLocationsPaginated', 'getAllTags', 'getBusiness', and 'getBusinessGroups'. Either include them or remove the dependency array.", ["1676"], "'scrollToTop' is assigned a value but never used.", "'handleClick' is assigned a value but never used.", "'handleSearch' is assigned a value but never used.", "'AUTH_REQUESTED' is defined but never used.", "'AUTH_SUCCESS' is defined but never used.", "'AUTH_LOGOUT' is defined but never used.", "'AUTH_ERROR' is defined but never used.", "'AUTH_UNAUTHORIZED' is defined but never used.", "'IRoleBasedAccessResponseModel' is defined but never used.", "'ILoggedInUserResponseModel' is defined but never used.", "'Card' is defined but never used.", "'CardContent' is defined but never used.", "'Pagination' is defined but never used.", "'Paper' is defined but never used.", "'OutlinedInput' is defined but never used.", "'getIn' is defined but never used.", "React Hook useEffect has missing dependencies: 'getBusiness', 'getBusinessGroups', and 'getLocationsList'. Either include them or remove the dependency array.", ["1677"], "'MenuProps' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'logoutUser'. Either include it or remove the dependency array.", ["1678"], ["1679"], "'Component' is defined but never used.", "no-useless-escape", "Unnecessary escape character: \\..", "Literal", "unnecessaryEscape", ["1680", "1681"], ["1682", "1683"], "'Toolbar' is defined but never used.", "'Typography' is defined but never used.", "'MenuIcon' is defined but never used.", "'Menu' is defined but never used.", "'MenuItem' is defined but never used.", "'ManageAccountsIcon' is defined but never used.", "'Collapse' is defined but never used.", "'SettingsOutlinedIcon' is defined but never used.", "'ArrowForwardIosRoundedIcon' is defined but never used.", "'ListAltSharp' is defined but never used.", "'MapsUgcRoundedIcon' is defined but never used.", "'AppBar' is assigned a value but never used.", "'theme' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'isMobile', 'menuOpened', 'openLeftMenu', 'rbAccess', and 'userInfo'. Either include them or remove the dependency array.", ["1684"], "'handleMenuItemClick' is assigned a value but never used.", "'GeoGridRoutes' is assigned a value but never used.", ["1685", "1686"], ["1687", "1688"], "'useEffect' is defined but never used.", "'useMemo' is defined but never used.", "'MONTHS' is assigned a value but never used.", "'PolarArea' is defined but never used.", "'Bar' is defined but never used.", "'ReviewService' is defined but never used.", "React Hook useEffect has missing dependencies: 'onDateChange' and 'selectedDuration'. Either include them or remove the dependency array. If 'onDateChange' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["1689"], "'ListItemText' is defined but never used.", "'AppBar' is defined but never used.", "'RoleType' is defined but never used.", "'CloseIcon' is defined but never used.", "'ArrowBackIos' is defined but never used.", "'ArrowForwardIos' is defined but never used.", "'LinearProgressWithLabel' is defined but never used.", "'open' is assigned a value but never used.", "'selectedValue' is assigned a value but never used.", "'selectedOptions' is assigned a value but never used.", "'handleOpen' is assigned a value but never used.", "'handleClose' is assigned a value but never used.", "'handleDropdownChange' is assigned a value but never used.", "'handleMultiSelectChange' is assigned a value but never used.", "'currentIndex' is assigned a value but never used.", "'handleNext' is assigned a value but never used.", "'handlePrev' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'getBusiness' and 'getLocationsList'. Either include them or remove the dependency array.", ["1690"], "React Hook useEffect has a missing dependency: 'initialValues'. Either include it or remove the dependency array.", ["1691"], "'LoadingContext' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchSearchKeywords'. Either include it or remove the dependency array.", ["1692"], "React Hook useEffect has a missing dependency: 'fetchMoreData'. Either include it or remove the dependency array.", ["1693"], "'SAVE_SCHEDULED' is defined but never used.", "'IGoogleCreatePost' is defined but never used.", "'DialogContent' is defined but never used.", "'Box' is defined but never used.", "'ListItemButton' is defined but never used.", "'ArrowBackIcon' is defined but never used.", "'ChevronRightIcon' is defined but never used.", "'MoreVertIcon' is defined but never used.", "'Formik' is defined but never used.", "'Form' is defined but never used.", "'Category' is defined but never used.", "'DialogTitle' is defined but never used.", "'AdapterDayjs' is defined but never used.", "'LocalizationProvider' is defined but never used.", "'DatePicker' is defined but never used.", "'ZoomInIcon' is defined but never used.", "'LIST_OF_LOCATIONS' is defined but never used.", "'LIST_OF_ROLE' is defined but never used.", "'useState' is defined but never used.", "'useNavigate' is defined but never used.", "'IUserResponseModel' is defined but never used.", "'IUser' is defined but never used.", "'IAlertDialogConfig' is defined but never used.", "'logOut' is defined but never used.", "'AlertDialog' is defined but never used.", "'isEdit' is assigned a value but never used.", "'setIsEdit' is assigned a value but never used.", "React Hook useEffect has missing dependencies: '_userService', 'getBusiness', 'getBusinessGroups', 'getLocationsList', and 'props.editData'. Either include them or remove the dependency array.", ["1694"], ["1695"], "'LIST_OF_BUSINESS' is defined but never used.", "'navigate' is assigned a value but never used.", "'Badge' is defined but never used.", "'Select' is defined but never used.", "'SelectChangeEvent' is defined but never used.", "'IRole' is defined but never used.", "'IBusinessGroup' is defined but never used.", "'ILocation' is defined but never used.", "'usersList' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchUsers'. Either include it or remove the dependency array.", ["1696"], "array-callback-return", "Array.prototype.map() expects a value to be returned at the end of arrow function.", "ArrowFunctionExpression", "expectedAtEnd", "'CircularProgress' is defined but never used.", "'MediaGallery' is defined but never used.", "'MISSING_INFORMATION' is defined but never used.", "'IconOnAvailability' is defined but never used.", "'LocationOnRoundedIcon' is defined but never used.", "'MovieIcon' is defined but never used.", "'AccountCircleIcon' is defined but never used.", "'NearMeOutlinedIcon' is defined but never used.", "'handleOpenMap' is assigned a value but never used.", "'FunctionComponent' is defined but never used.", "'ThumbUpAltRoundedIcon' is defined but never used.", "'FeedbackTemplate' is defined but never used.", "'FeedbackCard' is defined but never used.", "'CssBaseline' is defined but never used.", "'ImageBackgroundCard' is defined but never used.", "React Hook useEffect has missing dependencies: 'props.review.review', 'props.review.reviewerName', 'props.review.reviewerProfilePic', and 'props.review.starRating'. Either include them or remove the dependency array. If 'setPostTemplateConfig' needs the current value of 'props.review.review', you can also switch to useReducer instead of useState and read 'props.review.review' in the reducer.", ["1697"], "React Hook useEffect has a missing dependency: 'getAllTags'. Either include it or remove the dependency array.", ["1698"], "'createTag' is assigned a value but never used.", "'updateTagsToReviewResponse' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'size'. Either include it or remove the dependency array.", ["1699"], "no-lone-blocks", "Nested block is redundant.", "BlockStatement", "redundantNestedBlock", "'EditIcon' is defined but never used.", "'navigationType' is assigned a value but never used.", "'userInfo' is assigned a value but never used.", "'isValidElement' is defined but never used.", "'activeMenuItem' is assigned a value but never used.", "'LogoutIcon' is defined but never used.", "'logoutUser' is assigned a value but never used.", "'openSubMenu' is assigned a value but never used.", "React Hook useEffect has missing dependencies: '_locationService', 'props.mediaItems', and 'setLoading'. Either include them or remove the dependency array.", ["1700"], "React Hook useEffect has a missing dependency: 'props.profileImage'. Either include it or remove the dependency array.", ["1701"], "'fontType' is assigned a value but never used.", "'setFontType' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'props'. Either include it or remove the dependency array. However, 'props' will change when *any* prop changes, so the preferred fix is to destructure the 'props' object outside of the useEffect call and refer to those specific props inside useEffect.", ["1702"], ["1703"], "'ref' is defined but never used.", "'Rating' is defined but never used.", "'StarIcon' is defined but never used.", "jsx-a11y/alt-text", "img elements must have an alt prop, either with meaningful text, or an empty string for decorative images.", "'availableLocations' is assigned a value but never used.", "'FormControlLabel' is defined but never used.", "'SettingsIcon' is defined but never used.", "'ICreateReplyTemplateRequest' is defined but never used.", "'openAutoReplyDrawer' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadBusinesses'. Either include it or remove the dependency array.", ["1704"], "React Hook useEffect has missing dependencies: 'loadAutoReplySettings' and 'loadTemplates'. Either include them or remove the dependency array.", ["1705"], "'handleAutoReplyDrawerClose' is assigned a value but never used.", "'getStarRatingColor' is assigned a value but never used.", "'VisibilityIcon' is defined but never used.", "'ImageIcon' is defined but never used.", "'VideoLibraryIcon' is defined but never used.", ["1706"], "React Hook useEffect has a missing dependency: 'loadAssets'. Either include it or remove the dependency array.", ["1707"], "'isMobile' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'location.search', 'setToastConfig', 'startCountdown', 'title', and 'updateBusinessInstagramStatus'. Either include them or remove the dependency array.", ["1708"], "'Tooltip' is defined but never used.", "'Checkbox' is defined but never used.", "'VideoIcon' is defined but never used.", ["1709"], ["1710"], {"desc": "1711", "fix": "1712"}, {"desc": "1713", "fix": "1714"}, {"desc": "1711", "fix": "1715"}, {"desc": "1716", "fix": "1717"}, {"desc": "1718", "fix": "1719"}, {"desc": "1711", "fix": "1720"}, {"desc": "1711", "fix": "1721"}, {"desc": "1711", "fix": "1722"}, {"desc": "1723", "fix": "1724"}, {"desc": "1725", "fix": "1726"}, {"desc": "1727", "fix": "1728"}, {"desc": "1729", "fix": "1730"}, {"kind": "1731", "justification": "1732"}, {"desc": "1733", "fix": "1734"}, {"desc": "1735", "fix": "1736"}, {"desc": "1737", "fix": "1738"}, {"desc": "1739", "fix": "1740"}, {"desc": "1741", "fix": "1742"}, {"desc": "1743", "fix": "1744"}, {"desc": "1745", "fix": "1746"}, {"desc": "1747", "fix": "1748"}, {"desc": "1749", "fix": "1750"}, {"desc": "1751", "fix": "1752"}, {"desc": "1753", "fix": "1754"}, {"desc": "1725", "fix": "1755"}, {"desc": "1756", "fix": "1757"}, {"desc": "1758", "fix": "1759"}, {"desc": "1760", "fix": "1761"}, {"desc": "1762", "fix": "1763"}, {"desc": "1764", "fix": "1765"}, {"desc": "1766", "fix": "1767"}, {"desc": "1766", "fix": "1768"}, {"messageId": "1769", "fix": "1770", "desc": "1771"}, {"messageId": "1772", "fix": "1773", "desc": "1774"}, {"messageId": "1769", "fix": "1775", "desc": "1771"}, {"messageId": "1772", "fix": "1776", "desc": "1774"}, {"desc": "1777", "fix": "1778"}, {"messageId": "1769", "fix": "1779", "desc": "1771"}, {"messageId": "1772", "fix": "1780", "desc": "1774"}, {"messageId": "1769", "fix": "1781", "desc": "1771"}, {"messageId": "1772", "fix": "1782", "desc": "1774"}, {"desc": "1783", "fix": "1784"}, {"desc": "1785", "fix": "1786"}, {"desc": "1787", "fix": "1788"}, {"desc": "1789", "fix": "1790"}, {"desc": "1791", "fix": "1792"}, {"desc": "1793", "fix": "1794"}, {"desc": "1745", "fix": "1795"}, {"desc": "1796", "fix": "1797"}, {"desc": "1798", "fix": "1799"}, {"desc": "1800", "fix": "1801"}, {"desc": "1802", "fix": "1803"}, {"desc": "1804", "fix": "1805"}, {"desc": "1806", "fix": "1807"}, {"desc": "1808", "fix": "1809"}, {"desc": "1808", "fix": "1810"}, {"desc": "1811", "fix": "1812"}, {"desc": "1813", "fix": "1814"}, {"desc": "1815", "fix": "1816"}, {"desc": "1817", "fix": "1818"}, {"desc": "1819", "fix": "1820"}, {"desc": "1821", "fix": "1822"}, {"desc": "1817", "fix": "1823"}, "Update the dependencies array to be: []", {"range": "1824", "text": "1825"}, "Update the dependencies array to be: [loading, setLoading]", {"range": "1826", "text": "1827"}, {"range": "1828", "text": "1825"}, "Update the dependencies array to be: [userInfo, success, location.pathname, navigate, loginMessage]", {"range": "1829", "text": "1830"}, "Update the dependencies array to be: [dispatchSessionExpired, isUnAuthorised]", {"range": "1831", "text": "1832"}, {"range": "1833", "text": "1825"}, {"range": "1834", "text": "1825"}, {"range": "1835", "text": "1825"}, "Update the dependencies array to be: [open, setOpen, message, setToastMessage, toastConfig, setToastConfig]", {"range": "1836", "text": "1837"}, "Update the dependencies array to be: [title]", {"range": "1838", "text": "1839"}, "Update the dependencies array to be: [fetchLocationsPaginated, getBusiness, getBusinessGroups]", {"range": "1840", "text": "1841"}, "Update the dependencies array to be: [fetchLocations, getBusiness, getBusinessGroups]", {"range": "1842", "text": "1843"}, "directive", "", "Update the dependencies array to be: [location.state, title]", {"range": "1844", "text": "1845"}, "Update the dependencies array to be: [fetchLocations]", {"range": "1846", "text": "1847"}, "Update the dependencies array to be: [locationList, selectedLocationId]", {"range": "1848", "text": "1849"}, "Update the dependencies array to be: [selectedLocationId, selectedDateRange, selectedAccountId, fetchAnalyticsData, locationList]", {"range": "1850", "text": "1851"}, "Update the dependencies array to be: [loadSavedConfigurations, title, user]", {"range": "1852", "text": "1853"}, "Update the dependencies array to be: [navigate, userInfo.roleId]", {"range": "1854", "text": "1855"}, "Update the dependencies array to be: [getRolesList]", {"range": "1856", "text": "1857"}, "Update the dependencies array to be: [fetchUsersPaginated, paginationModel]", {"range": "1858", "text": "1859"}, "Update the dependencies array to be: [fetchBusinessPaginated, title, userInfo]", {"range": "1860", "text": "1861"}, "Update the dependencies array to be: [accountId, businessId, getLocationSummary, locationId]", {"range": "1862", "text": "1863"}, "Update the dependencies array to be: [locationSummary, performMissingInformationOperation]", {"range": "1864", "text": "1865"}, {"range": "1866", "text": "1839"}, "Update the dependencies array to be: [fetchLocationsPaginated, paginationModel]", {"range": "1867", "text": "1868"}, "Update the dependencies array to be: [getBusiness, getBusinessGroups]", {"range": "1869", "text": "1870"}, "Update the dependencies array to be: [gmbCallBack, searchParams]", {"range": "1871", "text": "1872"}, "Update the dependencies array to be: [fetchLocationsPaginated, getAllTags, getBusiness, getBusinessGroups]", {"range": "1873", "text": "1874"}, "Update the dependencies array to be: [getBusiness, getBusinessGroups, getLocationsList]", {"range": "1875", "text": "1876"}, "Update the dependencies array to be: [logoutUser, navigate]", {"range": "1877", "text": "1878"}, {"range": "1879", "text": "1878"}, "removeEscape", {"range": "1880", "text": "1732"}, "Remove the `\\`. This maintains the current functionality.", "escape<PERSON><PERSON><PERSON><PERSON>", {"range": "1881", "text": "1882"}, "Replace the `\\` with `\\\\` to include the actual backslash character.", {"range": "1883", "text": "1732"}, {"range": "1884", "text": "1882"}, "Update the dependencies array to be: [isMobile, menuOpened, openLeftMenu, rbAccess, userInfo]", {"range": "1885", "text": "1886"}, {"range": "1887", "text": "1732"}, {"range": "1888", "text": "1882"}, {"range": "1889", "text": "1732"}, {"range": "1890", "text": "1882"}, "Update the dependencies array to be: [onDateChange, selectedDuration]", {"range": "1891", "text": "1892"}, "Update the dependencies array to be: [getBusiness, getLocationsList]", {"range": "1893", "text": "1894"}, "Update the dependencies array to be: [initialValues.locationId, initialValues.accountId, locations, initialValues]", {"range": "1895", "text": "1896"}, "Update the dependencies array to be: [accountId, locationId, from, to, fetchSearchKeywords]", {"range": "1897", "text": "1898"}, "Update the dependencies array to be: [nextPageToken, loading, open, fetchMoreData]", {"range": "1899", "text": "1900"}, "Update the dependencies array to be: [_userService, getBusiness, getBusinessGroups, getLocationsList, props.editData]", {"range": "1901", "text": "1902"}, {"range": "1903", "text": "1857"}, "Update the dependencies array to be: [fetchUsers]", {"range": "1904", "text": "1905"}, "Update the dependencies array to be: [props.review.review, props.review.reviewerName, props.review.reviewerProfilePic, props.review.starRating]", {"range": "1906", "text": "1907"}, "Update the dependencies array to be: [getAllTags]", {"range": "1908", "text": "1909"}, "Update the dependencies array to be: [size]", {"range": "1910", "text": "1911"}, "Update the dependencies array to be: [_locationService, props.mediaItems, setLoading]", {"range": "1912", "text": "1913"}, "Update the dependencies array to be: [props.profileImage]", {"range": "1914", "text": "1915"}, "Update the dependencies array to be: [postTemplateConfig, props]", {"range": "1916", "text": "1917"}, {"range": "1918", "text": "1917"}, "Update the dependencies array to be: [loadBusinesses, userInfo]", {"range": "1919", "text": "1920"}, "Update the dependencies array to be: [loadAutoReplySettings, loadTemplates, selectedBusiness, userInfo]", {"range": "1921", "text": "1922"}, "Update the dependencies array to be: [loadBusinesses]", {"range": "1923", "text": "1924"}, "Update the dependencies array to be: [loadAssets, selectedBusinessId]", {"range": "1925", "text": "1926"}, "Update the dependencies array to be: [location.search, setToastConfig, startCountdown, title, updateBusinessInstagramStatus]", {"range": "1927", "text": "1928"}, "Update the dependencies array to be: [loadBusinesses, open]", {"range": "1929", "text": "1930"}, {"range": "1931", "text": "1926"}, [3829, 3838], "[]", [3942, 3951], "[loading, setLoading]", [4232, 4248], [4907, 4926], "[userInfo, success, location.pathname, navigate, loginMessage]", [5178, 5194], "[dispatchSessionExpired, isUnAuthorised]", [5709, 5715], [5840, 5846], [6029, 6042], [6221, 6249], "[open, setOpen, message, setToastMessage, toastConfig, setToastConfig]", [2979, 2981], "[title]", [5326, 5328], "[fetchLocationsPaginated, getBusiness, getBusinessGroups]", [5424, 5426], "[fetchLocations, getBusiness, getBusinessGroups]", [11692, 11694], "[location.state, title]", [4843, 4845], "[fetchLocations]", [5049, 5069], "[locationList, selectedLocationId]", [5446, 5485], "[selectedLocationId, selectedDateRange, selectedAccountId, fetchAnalyticsData, locationList]", [2227, 2240], "[loadSavedConfigurations, title, user]", [3559, 3561], "[navigate, userInfo.roleId]", [3614, 3616], "[getRolesList]", [5200, 5217], "[fetchUsersPaginated, paginationModel]", [5283, 5285], "[fetchBusinessPaginated, title, userInfo]", [5063, 5065], "[accountId, businessId, getLocationSummary, locationId]", [8294, 8311], "[locationSummary, performMissingInformationOperation]", [5560, 5562], [5626, 5643], "[fetchLocationsPaginated, paginationModel]", [5820, 5822], "[getBusiness, getBusinessGroups]", [1934, 1948], "[gmbCallBack, searchParams]", [7854, 7856], "[fetchLocationsPaginated, getAllTags, getBusiness, getBusinessGroups]", [4079, 4081], "[getBusiness, getBusinessGroups, getLocationsList]", [904, 914], "[logo<PERSON><PERSON><PERSON>, navigate]", [905, 915], [184, 185], [184, 184], "\\", [200, 201], [200, 200], [6518, 6520], "[isMobile, menuOpened, openLeftMenu, rbAccess, userInfo]", [288, 289], [288, 288], [304, 305], [304, 304], [1857, 1859], "[onDate<PERSON><PERSON>e, selectedDuration]", [6900, 6902], "[getBusiness, getLocationsList]", [7650, 7712], "[initialValues.locationId, initialValues.accountId, locations, initialValues]", [1519, 1552], "[accountId, locationId, from, to, fetchSearchKeywords]", [3743, 3773], "[nextPageToken, loading, open, fetchMoreData]", [5857, 5859], "[_userService, getBusiness, getBusinessGroups, getLocationsList, props.editData]", [12623, 12625], [3571, 3573], "[fetchUsers]", [2956, 2958], "[props.review.review, props.review.reviewerName, props.review.reviewerProfilePic, props.review.starRating]", [2469, 2471], "[getAllTags]", [497, 499], "[size]", [1388, 1390], "[_locationService, props.mediaItems, setLoading]", [1123, 1125], "[props.profileImage]", [1210, 1230], "[postTemplateConfig, props]", [6340, 6360], [3585, 3595], "[loadBusinesses, userInfo]", [3728, 3756], "[loadAutoReplySettings, loadTemplates, selectedBusiness, userInfo]", [3619, 3621], "[loadBusinesses]", [3750, 3770], "[loadAssets, selectedBusinessId]", [2035, 2037], "[location.search, setToastConfig, startCountdown, title, updateBusinessInstagramStatus]", [2472, 2478], "[loadBusinesses, open]", [2670, 2690]]